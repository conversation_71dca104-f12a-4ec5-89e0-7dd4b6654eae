import React from 'react';
import {
  Building,
  Store,
  Wrench,
  Factory,
  BarChart3,
  CreditCard,
  Star,
  Phone,
  Camera,
  MapPin,
  TrendingUp,
  TrendingDown,
  Minus,
  Search,
  Plus,
  ChevronRight,
  Users,
  Activity,
  Bell,
  Settings,
  ArrowLeft,
  ExternalLink,
  Filter,
  Download,
  Upload,
  Eye,
  Edit,
  Trash2,
  Check,
  X,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Calendar,
  Clock,
  Mail,
  MessageSquare,
  Smartphone,
  Globe,
  Shield,
  ShieldCheck,
  ShieldAlert,
  type LucideIcon,
} from 'lucide-react';

const iconMap: Record<string, LucideIcon> = {
  // Business types
  Building,
  Store,
  Tool: Wrench,
  Factory,

  // Data sources
  BarChart: BarChart3,
  CreditCard,
  Star,
  Phone,
  Camera,
  MapPin,
  
  // Trends
  TrendingUp,
  TrendingDown,
  Minus,
  
  // Actions
  Search,
  Plus,
  ChevronRight,
  ArrowLeft,
  ExternalLink,
  Filter,
  Download,
  Upload,
  Eye,
  Edit,
  Trash2,
  
  // Status
  Check,
  X,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  
  // General
  Users,
  Activity,
  Bell,
  Settings,
  Calendar,
  Clock,
  Mail,
  MessageSquare,
  Smartphone,
  Globe,
  
  // Security
  Shield,
  ShieldCheck,
  ShieldAlert,
};

interface IconProps {
  name: string;
  size?: number;
  color?: string;
  className?: string;
}

export const Icon: React.FC<IconProps> = ({
  name,
  size = 20,
  color = 'currentColor',
  className = ''
}) => {
  const IconComponent = iconMap[name];

  if (!IconComponent) {
    console.warn(`Icon "${name}" not found. Available icons:`, Object.keys(iconMap));
    return <div style={{ width: size, height: size }} />;
  }

  return (
    <IconComponent
      size={size}
      color={color}
      className={className}
    />
  );
};

export default Icon;
