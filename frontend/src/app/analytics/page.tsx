'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Heading,
  Text,
  Badge,
  Button,
  Flex,
} from '@chakra-ui/react';
import { DashboardAnalytics } from '@/types';
import { apiClient } from '@/lib/api';
import { formatNumber } from '@/lib/utils';

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<DashboardAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      console.log('Fetching analytics data...');
      const data = await apiClient.getDashboardAnalytics();
      console.log('Analytics data received:', data);
      setAnalytics(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const getRiskBandColor = (band: string) => {
    switch (band) {
      case 'green': return 'success.500';
      case 'yellow': return 'warning.500';
      case 'red': return 'danger.500';
      default: return 'neutral.500';
    }
  };

  const getRiskBandLabel = (band: string) => {
    switch (band) {
      case 'green': return 'Low Risk';
      case 'yellow': return 'Medium Risk';
      case 'red': return 'High Risk';
      default: return band;
    }
  };

  if (loading) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Flex justify="center" align="center" minH="400px">
            <Text fontSize="lg" fontWeight="600" color="neutral.700">
              Loading Analytics...
            </Text>
          </Flex>
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Box bg="white" p={8} borderRadius="xl" shadow="sm">
            <Text fontSize="lg" fontWeight="600" color="red.600" mb={2}>
              Failed to load analytics
            </Text>
            <Text fontSize="sm" color="red.500">
              {error}
            </Text>
          </Box>
        </Container>
      </Box>
    );
  }

  if (!analytics) {
    return null;
  }

  const totalRiskCount = analytics.risk_distribution.green +
                        analytics.risk_distribution.yellow +
                        analytics.risk_distribution.red;

  return (
    <Box bg="neutral.50" minH="100vh">
      <Container maxW="container.xl" py={8}>
        {/* Header */}
        <Flex justify="space-between" align="center" mb={8}>
          <Box>
            <Heading size="xl" color="neutral.800" fontWeight="700" mb={2}>
              Analytics Dashboard
            </Heading>
            <Text color="neutral.600" fontSize="sm">
              Last updated: {new Date(analytics.last_updated).toLocaleDateString()}
            </Text>
          </Box>
          <Button
            colorScheme="brand"
            size="lg"
            onClick={() => router.push('/portfolio')}
          >
            View Portfolio
          </Button>
        </Flex>

        {/* Key Metrics */}
        <Flex gap={6} mb={8} direction={{ base: 'column', lg: 'row' }}>
          <Box bg="white" p={6} borderRadius="xl" shadow="sm" flex={1}>
            <Text fontSize="sm" color="neutral.500" mb={1}>Total MSMEs</Text>
            <Text fontSize="3xl" fontWeight="700" color="neutral.800" mb={2}>
              {formatNumber(analytics.total_msmes)}
            </Text>
            <Button
              size="sm"
              variant="ghost"
              colorScheme="brand"
              onClick={() => router.push('/portfolio')}
            >
              View All →
            </Button>
          </Box>

          <Box bg="white" p={6} borderRadius="xl" shadow="sm" flex={1}>
            <Text fontSize="sm" color="neutral.500" mb={1}>Total Signals</Text>
            <Text fontSize="3xl" fontWeight="700" color="neutral.800" mb={2}>
              {formatNumber(analytics.total_signals)}
            </Text>
            <Text fontSize="sm" color="neutral.600">
              {analytics.average_signals_per_msme.toFixed(1)} avg per MSME
            </Text>
          </Box>

          <Box bg="white" p={6} borderRadius="xl" shadow="sm" flex={1}>
            <Text fontSize="sm" color="neutral.500" mb={1}>Low Risk MSMEs</Text>
            <Text fontSize="3xl" fontWeight="700" color="green.600" mb={2}>
              {analytics.risk_distribution.green}
            </Text>
            <Text fontSize="sm" color="neutral.600">
              {totalRiskCount > 0 ? ((analytics.risk_distribution.green / totalRiskCount) * 100).toFixed(1) : 0}% of portfolio
            </Text>
          </Box>

          <Box bg="white" p={6} borderRadius="xl" shadow="sm" flex={1}>
            <Text fontSize="sm" color="neutral.500" mb={1}>High Risk MSMEs</Text>
            <Text fontSize="3xl" fontWeight="700" color="red.600" mb={2}>
              {analytics.risk_distribution.red}
            </Text>
            <Text fontSize="sm" color="neutral.600">
              {totalRiskCount > 0 ? ((analytics.risk_distribution.red / totalRiskCount) * 100).toFixed(1) : 0}% of portfolio
            </Text>
          </Box>
        </Flex>

        {/* Distribution Charts */}
        <Flex gap={6} direction={{ base: 'column', lg: 'row' }}>
          <Box bg="white" p={6} borderRadius="xl" shadow="sm" flex={1}>
            <Heading size="md" color="neutral.800" mb={4}>
              Risk Distribution
            </Heading>
            <Box>
              {Object.entries(analytics.risk_distribution).map(([risk, count]) => (
                <Flex key={risk} justify="space-between" align="center" mb={3}>
                  <Flex align="center" gap={3}>
                    <Badge
                      colorScheme={risk === 'green' ? 'green' : risk === 'yellow' ? 'yellow' : 'red'}
                      variant="subtle"
                      px={2}
                      py={1}
                      fontSize="xs"
                    >
                      {getRiskBandLabel(risk)}
                    </Badge>
                    <Text fontSize="sm" color="neutral.600">
                      {count} MSMEs
                    </Text>
                  </Flex>
                  <Text fontSize="sm" fontWeight="600" color="neutral.700">
                    {totalRiskCount > 0 ? ((count / totalRiskCount) * 100).toFixed(1) : 0}%
                  </Text>
                </Flex>
              ))}
            </Box>
          </Box>

          <Box bg="white" p={6} borderRadius="xl" shadow="sm" flex={1}>
            <Heading size="md" color="neutral.800" mb={4}>
              Business Type Distribution
            </Heading>
            <Box>
              {Object.entries(analytics.business_type_distribution).map(([type, count]) => (
                <Flex key={type} justify="space-between" align="center" mb={3}>
                  <Flex align="center" gap={3}>
                    <Text fontSize="sm" color="neutral.700" textTransform="capitalize" fontWeight="500">
                      {type}
                    </Text>
                    <Text fontSize="sm" color="neutral.600">
                      {count} MSMEs
                    </Text>
                  </Flex>
                  <Text fontSize="sm" fontWeight="600" color="neutral.700">
                    {analytics.total_msmes > 0 ? ((count / analytics.total_msmes) * 100).toFixed(1) : 0}%
                  </Text>
                </Flex>
              ))}
            </Box>
          </Box>
        </Flex>
      </Container>
    </Box>
  );
}