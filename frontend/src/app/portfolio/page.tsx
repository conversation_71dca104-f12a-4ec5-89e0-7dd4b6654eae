'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Heading,
  Text,
  Badge,
  Button,
  Flex,
  Input,
} from '@chakra-ui/react';
import { MSMEProfile } from '@/types';
import { apiClient } from '@/lib/api';
import {
  getRiskBandColor,
  getRiskBandColorScheme,
  formatScore,
  formatDate,
  getBusinessTypeIcon,
  getTrendIcon,
  getTrendColor,
} from '@/lib/utils';

export default function PortfolioPage() {
  const [msmes, setMsmes] = useState<MSMEProfile[]>([]);
  const [filteredMsmes, setFilteredMsmes] = useState<MSMEProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [riskFilter, setRiskFilter] = useState('all');
  const [businessTypeFilter, setBusinessTypeFilter] = useState('all');
  
  const router = useRouter();

  const fetchPortfolio = async () => {
    try {
      setLoading(true);
      console.log('Fetching portfolio data...');
      const data = await apiClient.getPortfolio();
      console.log('Portfolio data received:', data);
      setMsmes(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching portfolio:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch portfolio');
    } finally {
      setLoading(false);
    }
  };

  const filterMsmes = useCallback(() => {
    let filtered = msmes;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(msme =>
        msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msme.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Risk band filter
    if (riskFilter !== 'all') {
      filtered = filtered.filter(msme => msme.risk_band === riskFilter);
    }

    // Business type filter
    if (businessTypeFilter !== 'all') {
      filtered = filtered.filter(msme => msme.business_type === businessTypeFilter);
    }

    setFilteredMsmes(filtered);
  }, [msmes, searchTerm, riskFilter, businessTypeFilter]);

  useEffect(() => {
    fetchPortfolio();
  }, []);

  useEffect(() => {
    filterMsmes();
  }, [filterMsmes]);

  const handleMSMEClick = (msmeId: string) => {
    router.push(`/msme/${msmeId}`);
  };

  const getRiskBandLabel = (riskBand: string) => {
    switch (riskBand) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return riskBand;
    }
  };

  if (loading) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Flex justify="center" align="center" minH="400px">
            <Box textAlign="center">
              <Text fontSize="lg" fontWeight="600" color="neutral.700" mb={4}>
                Loading Portfolio...
              </Text>
            </Box>
          </Flex>
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box bg="neutral.50" minH="100vh">
        <Container maxW="container.xl" py={8}>
          <Box bg="white" p={8} borderRadius="xl" shadow="sm">
            <Text fontSize="lg" fontWeight="600" color="red.600" mb={2}>
              Failed to load portfolio
            </Text>
            <Text fontSize="sm" color="red.500">
              {error}
            </Text>
          </Box>
        </Container>
      </Box>
    );
  }

  return (
    <Box bg="neutral.50" minH="100vh">
      <Container maxW="container.xl" py={8}>
        {/* Header */}
        <Flex justify="space-between" align="center" mb={8}>
          <Box>
            <Heading size="xl" color="neutral.800" fontWeight="700" mb={2}>
              Portfolio Dashboard
            </Heading>
            <Text color="neutral.600" fontSize="sm">
              {filteredMsmes.length} of {msmes.length} MSMEs • Last updated: {new Date().toLocaleDateString()}
            </Text>
          </Box>
          <Button
            colorScheme="brand"
            size="lg"
            onClick={() => router.push('/msme/new')}
          >
            Add MSME
          </Button>
        </Flex>

        {/* Filters */}
        <Box bg="white" p={6} borderRadius="xl" shadow="sm" mb={6}>
          <Flex gap={4} direction={{ base: 'column', md: 'row' }}>
            <Input
              placeholder="Search MSMEs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              bg="neutral.50"
              flex={1}
            />
            <select
              value={riskFilter}
              onChange={(e) => setRiskFilter(e.target.value)}
              style={{
                padding: '8px 12px',
                borderRadius: '8px',
                border: '1px solid #e2e8f0',
                backgroundColor: '#f7fafc',
                minWidth: '150px'
              }}
            >
              <option value="all">All Risk Bands</option>
              <option value="red">High Risk</option>
              <option value="yellow">Medium Risk</option>
              <option value="green">Low Risk</option>
            </select>
            <select
              value={businessTypeFilter}
              onChange={(e) => setBusinessTypeFilter(e.target.value)}
              style={{
                padding: '8px 12px',
                borderRadius: '8px',
                border: '1px solid #e2e8f0',
                backgroundColor: '#f7fafc',
                minWidth: '150px'
              }}
            >
              <option value="all">All Business Types</option>
              <option value="retail">Retail</option>
              <option value="b2b">B2B</option>
              <option value="services">Services</option>
              <option value="manufacturing">Manufacturing</option>
            </select>
          </Flex>
        </Box>

        {/* MSME List */}
        <Box bg="white" borderRadius="xl" shadow="sm">
          {filteredMsmes.map((msme) => (
            <Box
              key={msme.msme_id}
              p={6}
              borderBottom="1px solid"
              borderColor="neutral.200"
              cursor="pointer"
              onClick={() => handleMSMEClick(msme.msme_id)}
              _hover={{ bg: 'neutral.50' }}
              _last={{ borderBottom: 'none' }}
            >
              <Flex justify="space-between" align="center">
                <Box>
                  <Text fontWeight="600" fontSize="lg" mb={1}>
                    {msme.name}
                  </Text>
                  <Text fontSize="sm" color="neutral.600" mb={2}>
                    {msme.business_type} • {msme.location}
                  </Text>
                  <Flex gap={4} align="center">
                    <Box>
                      <Text fontSize="xs" color="neutral.500">Credit Score</Text>
                      <Text fontSize="xl" fontWeight="700" color={getRiskBandColor(msme.risk_band)}>
                        {formatScore(msme.current_score)}
                      </Text>
                    </Box>
                    <Box>
                      <Text fontSize="xs" color="neutral.500">Risk</Text>
                      <Badge colorScheme={getRiskBandColorScheme(msme.risk_band)} variant="subtle">
                        {getRiskBandLabel(msme.risk_band)}
                      </Badge>
                    </Box>
                    <Box>
                      <Text fontSize="xs" color="neutral.500">Signals</Text>
                      <Text fontSize="sm" fontWeight="600">{msme.signals_count}</Text>
                    </Box>
                  </Flex>
                </Box>
                <Box textAlign="right">
                  <Text fontSize="xs" color="neutral.500" mb={1}>Trend</Text>
                  <Text
                    fontSize="sm"
                    color={getTrendColor(msme.score_trend)}
                    fontWeight="600"
                    textTransform="capitalize"
                  >
                    {msme.score_trend}
                  </Text>
                  {msme.last_signal_date && (
                    <Text fontSize="xs" color="neutral.400" mt={2}>
                      Last: {formatDate(msme.last_signal_date)}
                    </Text>
                  )}
                </Box>
              </Flex>
            </Box>
          ))}
        </Box>

        {filteredMsmes.length === 0 && (
          <Box textAlign="center" py={12} bg="white" borderRadius="xl" shadow="sm">
            <Text fontSize="lg" fontWeight="600" color="neutral.600" mb={2}>
              No MSMEs found
            </Text>
            <Text fontSize="sm" color="neutral.500">
              Try adjusting your search criteria or filters
            </Text>
          </Box>
        )}
      </Container>
    </Box>
  );
}
