/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/analytics/page";
exports.ids = ["app/analytics/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/analytics/page.tsx */ \"(rsc)/./src/app/analytics/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'analytics',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/analytics/page\",\n        pathname: \"/analytics\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fanalytics%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fanalytics%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/analytics/page.tsx */ \"(rsc)/./src/app/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFuaWt1bWFyZ291bmklMkZEb2N1bWVudHMlMkZDcmVkaXQtQ2hha3JhLUZpbmFsJTJGY3JlZGl0LWNoYWtyYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGYW5hbHl0aWNzJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUEwSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hbmlrdW1hcmdvdW5pL0RvY3VtZW50cy9DcmVkaXQtQ2hha3JhLUZpbmFsL2NyZWRpdC1jaGFrcmEvZnJvbnRlbmQvc3JjL2FwcC9hbmFseXRpY3MvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fanalytics%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fprovider.tsx%22%2C%22ids%22%3A%5B%22Provider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fprovider.tsx%22%2C%22ids%22%3A%5B%22Provider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(rsc)/./src/components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/provider.tsx */ \"(rsc)/./src/components/ui/provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFuaWt1bWFyZ291bmklMkZEb2N1bWVudHMlMkZDcmVkaXQtQ2hha3JhLUZpbmFsJTJGY3JlZGl0LWNoYWtyYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtYW5pa3VtYXJnb3VuaSUyRkRvY3VtZW50cyUyRkNyZWRpdC1DaGFrcmEtRmluYWwlMkZjcmVkaXQtY2hha3JhJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFuaWt1bWFyZ291bmklMkZEb2N1bWVudHMlMkZDcmVkaXQtQ2hha3JhLUZpbmFsJTJGY3JlZGl0LWNoYWtyYSUyRmZyb250ZW5kJTJGc3JjJTJGY29tcG9uZW50cyUyRnVpJTJGcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUEwSztBQUMxSztBQUNBLDRLQUE0SyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9tYW5pa3VtYXJnb3VuaS9Eb2N1bWVudHMvQ3JlZGl0LUNoYWtyYS1GaW5hbC9jcmVkaXQtY2hha3JhL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL05hdmlnYXRpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9tYW5pa3VtYXJnb3VuaS9Eb2N1bWVudHMvQ3JlZGl0LUNoYWtyYS1GaW5hbC9jcmVkaXQtY2hha3JhL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL3VpL3Byb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fprovider.tsx%22%2C%22ids%22%3A%5B%22Provider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYW5pa3VtYXJnb3VuaS9Eb2N1bWVudHMvQ3JlZGl0LUNoYWtyYS1GaW5hbC9jcmVkaXQtY2hha3JhL2Zyb250ZW5kL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbWFuaWt1bWFyZ291bmkvRG9jdW1lbnRzL0NyZWRpdC1DaGFrcmEtRmluYWwvY3JlZGl0LWNoYWtyYS9mcm9udGVuZC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/provider */ \"(rsc)/./src/components/ui/provider.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Credit Chakra - MSME Credit Manager\",\n    description: \"Credit scoring and monitoring platform for MSMEs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_provider__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBbUQ7QUFDRjtBQUMxQjtBQUVoQixNQUFNRSxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0Qsd0JBQXdCO3NCQUM1Qiw0RUFBQ1QsNkRBQVFBOztrQ0FDUCw4REFBQ0MsOERBQVVBOzs7OztvQkFDVks7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYW5pa3VtYXJnb3VuaS9Eb2N1bWVudHMvQ3JlZGl0LUNoYWtyYS1GaW5hbC9jcmVkaXQtY2hha3JhL2Zyb250ZW5kL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm92aWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvcHJvdmlkZXJcIlxuaW1wb3J0IE5hdmlnYXRpb24gZnJvbSBcIkAvY29tcG9uZW50cy9OYXZpZ2F0aW9uXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ3JlZGl0IENoYWtyYSAtIE1TTUUgQ3JlZGl0IE1hbmFnZXJcIixcbiAgZGVzY3JpcHRpb246IFwiQ3JlZGl0IHNjb3JpbmcgYW5kIG1vbml0b3JpbmcgcGxhdGZvcm0gZm9yIE1TTUVzXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5IHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgICAgPFByb3ZpZGVyPlxuICAgICAgICAgIDxOYXZpZ2F0aW9uIC8+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1Byb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJQcm92aWRlciIsIk5hdmlnYXRpb24iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImJvZHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/provider.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Provider: () => (/* binding */ Provider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Provider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Provider() from the server but Provider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/provider.tsx",
"Provider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fanalytics%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fanalytics%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/analytics/page.tsx */ \"(ssr)/./src/app/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFuaWt1bWFyZ291bmklMkZEb2N1bWVudHMlMkZDcmVkaXQtQ2hha3JhLUZpbmFsJTJGY3JlZGl0LWNoYWtyYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGYW5hbHl0aWNzJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUEwSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hbmlrdW1hcmdvdW5pL0RvY3VtZW50cy9DcmVkaXQtQ2hha3JhLUZpbmFsL2NyZWRpdC1jaGFrcmEvZnJvbnRlbmQvc3JjL2FwcC9hbmFseXRpY3MvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fanalytics%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fprovider.tsx%22%2C%22ids%22%3A%5B%22Provider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fprovider.tsx%22%2C%22ids%22%3A%5B%22Provider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/provider.tsx */ \"(ssr)/./src/components/ui/provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFuaWt1bWFyZ291bmklMkZEb2N1bWVudHMlMkZDcmVkaXQtQ2hha3JhLUZpbmFsJTJGY3JlZGl0LWNoYWtyYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZtYW5pa3VtYXJnb3VuaSUyRkRvY3VtZW50cyUyRkNyZWRpdC1DaGFrcmEtRmluYWwlMkZjcmVkaXQtY2hha3JhJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFuaWt1bWFyZ291bmklMkZEb2N1bWVudHMlMkZDcmVkaXQtQ2hha3JhLUZpbmFsJTJGY3JlZGl0LWNoYWtyYSUyRmZyb250ZW5kJTJGc3JjJTJGY29tcG9uZW50cyUyRnVpJTJGcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUEwSztBQUMxSztBQUNBLDRLQUE0SyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9tYW5pa3VtYXJnb3VuaS9Eb2N1bWVudHMvQ3JlZGl0LUNoYWtyYS1GaW5hbC9jcmVkaXQtY2hha3JhL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL05hdmlnYXRpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9tYW5pa3VtYXJnb3VuaS9Eb2N1bWVudHMvQ3JlZGl0LUNoYWtyYS1GaW5hbC9jcmVkaXQtY2hha3JhL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL3VpL3Byb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2FNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fcomponents%2Fui%2Fprovider.tsx%22%2C%22ids%22%3A%5B%22Provider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction AnalyticsPage() {\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fetchAnalytics = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching analytics data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getDashboardAnalytics();\n            console.log('Analytics data received:', data);\n            setAnalytics(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching analytics:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch analytics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPage.useEffect\": ()=>{\n            fetchAnalytics();\n        }\n    }[\"AnalyticsPage.useEffect\"], []);\n    const getRiskBandColor = (band)=>{\n        switch(band){\n            case 'green':\n                return 'success.500';\n            case 'yellow':\n                return 'warning.500';\n            case 'red':\n                return 'danger.500';\n            default:\n                return 'neutral.500';\n        }\n    };\n    const getRiskBandLabel = (band)=>{\n        switch(band){\n            case 'green':\n                return 'Low Risk';\n            case 'yellow':\n                return 'Medium Risk';\n            case 'red':\n                return 'High Risk';\n            default:\n                return band;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                        fontSize: \"lg\",\n                        fontWeight: \"600\",\n                        color: \"neutral.700\",\n                        children: \"Loading Analytics...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"lg\",\n                            fontWeight: \"600\",\n                            color: \"red.600\",\n                            mb: 2,\n                            children: \"Failed to load analytics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"sm\",\n                            color: \"red.500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (!analytics) {\n        return null;\n    }\n    const totalRiskCount = analytics.risk_distribution.green + analytics.risk_distribution.yellow + analytics.risk_distribution.red;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    mb: 2,\n                                    children: \"Analytics Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    color: \"neutral.600\",\n                                    fontSize: \"sm\",\n                                    children: [\n                                        \"Last updated: \",\n                                        new Date(analytics.last_updated).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/portfolio'),\n                            children: \"View Portfolio\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    gap: 6,\n                    mb: 8,\n                    direction: {\n                        base: 'column',\n                        lg: 'row'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Total MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"neutral.800\",\n                                    mb: 2,\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_msmes)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    colorScheme: \"brand\",\n                                    onClick: ()=>router.push('/portfolio'),\n                                    children: \"View All →\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Total Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"neutral.800\",\n                                    mb: 2,\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_signals)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        analytics.average_signals_per_msme.toFixed(1),\n                                        \" avg per MSME\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Low Risk MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"green.600\",\n                                    mb: 2,\n                                    children: analytics.risk_distribution.green\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        totalRiskCount > 0 ? (analytics.risk_distribution.green / totalRiskCount * 100).toFixed(1) : 0,\n                                        \"% of portfolio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"High Risk MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"red.600\",\n                                    mb: 2,\n                                    children: analytics.risk_distribution.red\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        totalRiskCount > 0 ? (analytics.risk_distribution.red / totalRiskCount * 100).toFixed(1) : 0,\n                                        \"% of portfolio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    gap: 6,\n                    direction: {\n                        base: 'column',\n                        lg: 'row'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"md\",\n                                    color: \"neutral.800\",\n                                    mb: 4,\n                                    children: \"Risk Distribution\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                    children: Object.entries(analytics.risk_distribution).map(([risk, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                            justify: \"space-between\",\n                                            align: \"center\",\n                                            mb: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                                    align: \"center\",\n                                                    gap: 3,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                            colorScheme: risk === 'green' ? 'green' : risk === 'yellow' ? 'yellow' : 'red',\n                                                            variant: \"subtle\",\n                                                            px: 2,\n                                                            py: 1,\n                                                            fontSize: \"xs\",\n                                                            children: getRiskBandLabel(risk)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"sm\",\n                                                            color: \"neutral.600\",\n                                                            children: [\n                                                                count,\n                                                                \" MSMEs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"sm\",\n                                                    fontWeight: \"600\",\n                                                    color: \"neutral.700\",\n                                                    children: [\n                                                        totalRiskCount > 0 ? (count / totalRiskCount * 100).toFixed(1) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, risk, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"md\",\n                                    color: \"neutral.800\",\n                                    mb: 4,\n                                    children: \"Business Type Distribution\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                    children: Object.entries(analytics.business_type_distribution).map(([type, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                            justify: \"space-between\",\n                                            align: \"center\",\n                                            mb: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                                    align: \"center\",\n                                                    gap: 3,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"sm\",\n                                                            color: \"neutral.700\",\n                                                            textTransform: \"capitalize\",\n                                                            fontWeight: \"500\",\n                                                            children: type\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"sm\",\n                                                            color: \"neutral.600\",\n                                                            children: [\n                                                                count,\n                                                                \" MSMEs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"sm\",\n                                                    fontWeight: \"600\",\n                                                    color: \"neutral.700\",\n                                                    children: [\n                                                        analytics.total_msmes > 0 ? (count / analytics.total_msmes * 100).toFixed(1) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/analytics/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/stack/h-stack.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/link/link.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/spacer/spacer.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Container,Flex,HStack,Link,Spacer,Text!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_Icon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Icon */ \"(ssr)/./src/components/ui/Icon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navigation() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isActive = (path)=>pathname === path;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n        bg: \"white\",\n        borderBottom: \"1px\",\n        borderColor: \"neutral.200\",\n        position: \"sticky\",\n        top: 0,\n        zIndex: 10,\n        shadow: \"sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Container, {\n            maxW: \"container.xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Flex, {\n                h: 16,\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                        spacing: 8,\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Link, {\n                                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                href: \"/portfolio\",\n                                _hover: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                    spacing: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                            w: 8,\n                                            h: 8,\n                                            bg: \"brand.600\",\n                                            borderRadius: \"lg\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                name: \"Shield\",\n                                                size: 18,\n                                                color: \"white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            fontSize: \"xl\",\n                                            fontWeight: \"bold\",\n                                            color: \"neutral.800\",\n                                            children: \"Credit Chakra\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                as: \"nav\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Link, {\n                                        as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                        href: \"/portfolio\",\n                                        px: 4,\n                                        py: 2,\n                                        rounded: \"lg\",\n                                        fontWeight: \"600\",\n                                        fontSize: \"sm\",\n                                        bg: isActive('/portfolio') ? 'brand.50' : 'transparent',\n                                        color: isActive('/portfolio') ? 'brand.700' : 'neutral.600',\n                                        border: \"1px solid\",\n                                        borderColor: isActive('/portfolio') ? 'brand.200' : 'transparent',\n                                        _hover: {\n                                            textDecoration: 'none',\n                                            bg: isActive('/portfolio') ? 'brand.100' : 'neutral.50',\n                                            color: isActive('/portfolio') ? 'brand.800' : 'brand.600'\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                            spacing: 2,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                    name: \"Users\",\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                    children: \"Portfolio\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Link, {\n                                        as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                                        href: \"/analytics\",\n                                        px: 4,\n                                        py: 2,\n                                        rounded: \"lg\",\n                                        fontWeight: \"600\",\n                                        fontSize: \"sm\",\n                                        bg: isActive('/analytics') ? 'brand.50' : 'transparent',\n                                        color: isActive('/analytics') ? 'brand.700' : 'neutral.600',\n                                        border: \"1px solid\",\n                                        borderColor: isActive('/analytics') ? 'brand.200' : 'transparent',\n                                        _hover: {\n                                            textDecoration: 'none',\n                                            bg: isActive('/analytics') ? 'brand.100' : 'neutral.50',\n                                            color: isActive('/analytics') ? 'brand.800' : 'brand.600'\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                            spacing: 2,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                    name: \"BarChart\",\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                    children: \"Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Spacer, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                        spacing: 4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Container_Flex_HStack_Link_Spacer_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                            colorScheme: \"brand\",\n                            variant: \"subtle\",\n                            px: 3,\n                            py: 1,\n                            borderRadius: \"full\",\n                            fontSize: \"xs\",\n                            fontWeight: \"600\",\n                            children: \"Credit Manager\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/Navigation.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Icon.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Icon.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/factory.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,BarChart3,Bell,Building,Calendar,Camera,Check,CheckCircle,ChevronRight,Clock,CreditCard,Download,Edit,ExternalLink,Eye,Factory,Filter,Globe,Info,Mail,MapPin,MessageSquare,Minus,Phone,Plus,Search,Settings,Shield,ShieldAlert,ShieldCheck,Smartphone,Star,Store,Trash2,TrendingDown,TrendingUp,Upload,Users,Wrench,X,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-alert.js\");\n\n\n\nconst iconMap = {\n    // Business types\n    Building: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    Store: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    Tool: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    Factory: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    // Data sources\n    BarChart: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    CreditCard: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    Star: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    Phone: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Camera: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    MapPin: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    // Trends\n    TrendingUp: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    TrendingDown: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    Minus: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    // Actions\n    Search: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    Plus: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    ChevronRight: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    ArrowLeft: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    ExternalLink: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    Filter: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    Download: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    Upload: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    Eye: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    Edit: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    Trash2: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    // Status\n    Check: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n    X: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n    AlertCircle: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n    Info: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n    CheckCircle: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n    XCircle: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n    // General\n    Users: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n    Activity: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n    Bell: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n    Settings: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n    Calendar: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n    Clock: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n    Mail: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n    MessageSquare: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"],\n    Smartphone: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n    Globe: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n    // Security\n    Shield: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n    ShieldCheck: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n    ShieldAlert: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_BarChart3_Bell_Building_Calendar_Camera_Check_CheckCircle_ChevronRight_Clock_CreditCard_Download_Edit_ExternalLink_Eye_Factory_Filter_Globe_Info_Mail_MapPin_MessageSquare_Minus_Phone_Plus_Search_Settings_Shield_ShieldAlert_ShieldCheck_Smartphone_Star_Store_Trash2_TrendingDown_TrendingUp_Upload_Users_Wrench_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"]\n};\nconst Icon = ({ name, size = 20, color = 'currentColor', className = '' })=>{\n    const IconComponent = iconMap[name];\n    if (!IconComponent) {\n        console.warn(`Icon \"${name}\" not found. Available icons:`, Object.keys(iconMap));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: size,\n                height: size\n            }\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/Icon.tsx\",\n            lineNumber: 125,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n        size: size,\n        color: color,\n        className: className\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/Icon.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Icon);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Icon.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/color-mode.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/color-mode.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorModeButton: () => (/* binding */ ColorModeButton),\n/* harmony export */   ColorModeIcon: () => (/* binding */ ColorModeIcon),\n/* harmony export */   ColorModeProvider: () => (/* binding */ ColorModeProvider),\n/* harmony export */   DarkMode: () => (/* binding */ DarkMode),\n/* harmony export */   LightMode: () => (/* binding */ LightMode),\n/* harmony export */   useColorMode: () => (/* binding */ useColorMode),\n/* harmony export */   useColorModeValue: () => (/* binding */ useColorModeValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClientOnly,IconButton,Skeleton,Span!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/client-only/client-only.js\");\n/* harmony import */ var _barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ClientOnly,IconButton,Skeleton,Span!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/skeleton/skeleton.js\");\n/* harmony import */ var _barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ClientOnly,IconButton,Skeleton,Span!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/button/icon-button.js\");\n/* harmony import */ var _barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ClientOnly,IconButton,Skeleton,Span!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/components/box/span.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_LuMoon_LuSun_react_icons_lu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=LuMoon,LuSun!=!react-icons/lu */ \"(ssr)/./node_modules/react-icons/lu/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ColorModeProvider,useColorMode,useColorModeValue,ColorModeIcon,ColorModeButton,LightMode,DarkMode auto */ \n\n\n\n\nfunction ColorModeProvider(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        disableTransitionOnChange: true,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction useColorMode() {\n    const { resolvedTheme, setTheme, forcedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const colorMode = forcedTheme || resolvedTheme;\n    const toggleColorMode = ()=>{\n        setTheme(resolvedTheme === \"dark\" ? \"light\" : \"dark\");\n    };\n    return {\n        colorMode: colorMode,\n        setColorMode: setTheme,\n        toggleColorMode\n    };\n}\nfunction useColorModeValue(light, dark) {\n    const { colorMode } = useColorMode();\n    return colorMode === \"dark\" ? dark : light;\n}\nfunction ColorModeIcon() {\n    const { colorMode } = useColorMode();\n    return colorMode === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuMoon_LuSun_react_icons_lu__WEBPACK_IMPORTED_MODULE_3__.LuMoon, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n        lineNumber: 46,\n        columnNumber: 33\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuMoon_LuSun_react_icons_lu__WEBPACK_IMPORTED_MODULE_3__.LuSun, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n        lineNumber: 46,\n        columnNumber: 46\n    }, this);\n}\nconst ColorModeButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function ColorModeButton(props, ref) {\n    const { toggleColorMode } = useColorMode();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ClientOnly, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n            boxSize: \"8\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n            lineNumber: 57,\n            columnNumber: 27\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n            onClick: toggleColorMode,\n            variant: \"ghost\",\n            \"aria-label\": \"Toggle color mode\",\n            size: \"sm\",\n            ref: ref,\n            ...props,\n            css: {\n                _icon: {\n                    width: \"5\",\n                    height: \"5\"\n                }\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorModeIcon, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n});\nconst LightMode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function LightMode(props, ref) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Span, {\n        color: \"fg\",\n        display: \"contents\",\n        className: \"chakra-theme light\",\n        colorPalette: \"gray\",\n        colorScheme: \"light\",\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n        lineNumber: 81,\n        columnNumber: 7\n    }, this);\n});\nconst DarkMode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function DarkMode(props, ref) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClientOnly_IconButton_Skeleton_Span_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Span, {\n        color: \"fg\",\n        display: \"contents\",\n        className: \"chakra-theme dark\",\n        colorPalette: \"gray\",\n        colorScheme: \"dark\",\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/color-mode.tsx\",\n        lineNumber: 97,\n        columnNumber: 7\n    }, this);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/color-mode.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/provider.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ChakraProvider_defaultSystem_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChakraProvider,defaultSystem!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/styled-system/provider.js\");\n/* harmony import */ var _barrel_optimize_names_ChakraProvider_defaultSystem_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChakraProvider,defaultSystem!=!@chakra-ui/react */ \"(ssr)/./node_modules/@chakra-ui/react/dist/esm/preset.js\");\n/* harmony import */ var _color_mode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./color-mode */ \"(ssr)/./src/components/ui/color-mode.tsx\");\n/* __next_internal_client_entry_do_not_use__ Provider auto */ \n\n\nfunction Provider(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChakraProvider_defaultSystem_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ChakraProvider, {\n        value: _barrel_optimize_names_ChakraProvider_defaultSystem_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.defaultSystem,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_color_mode__WEBPACK_IMPORTED_MODULE_1__.ColorModeProvider, {\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/provider.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/components/ui/provider.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVnRTtBQUkzQztBQUVkLFNBQVNHLFNBQVNDLEtBQTZCO0lBQ3BELHFCQUNFLDhEQUFDSiwrR0FBY0E7UUFBQ0ssT0FBT0osOEdBQWFBO2tCQUNsQyw0RUFBQ0MsMERBQWlCQTtZQUFFLEdBQUdFLEtBQUs7Ozs7Ozs7Ozs7O0FBR2xDIiwic291cmNlcyI6WyIvVXNlcnMvbWFuaWt1bWFyZ291bmkvRG9jdW1lbnRzL0NyZWRpdC1DaGFrcmEtRmluYWwvY3JlZGl0LWNoYWtyYS9mcm9udGVuZC9zcmMvY29tcG9uZW50cy91aS9wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgQ2hha3JhUHJvdmlkZXIsIGRlZmF1bHRTeXN0ZW0gfSBmcm9tIFwiQGNoYWtyYS11aS9yZWFjdFwiXG5pbXBvcnQge1xuICBDb2xvck1vZGVQcm92aWRlcixcbiAgdHlwZSBDb2xvck1vZGVQcm92aWRlclByb3BzLFxufSBmcm9tIFwiLi9jb2xvci1tb2RlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVyKHByb3BzOiBDb2xvck1vZGVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPENoYWtyYVByb3ZpZGVyIHZhbHVlPXtkZWZhdWx0U3lzdGVtfT5cbiAgICAgIDxDb2xvck1vZGVQcm92aWRlciB7Li4ucHJvcHN9IC8+XG4gICAgPC9DaGFrcmFQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkNoYWtyYVByb3ZpZGVyIiwiZGVmYXVsdFN5c3RlbSIsIkNvbG9yTW9kZVByb3ZpZGVyIiwiUHJvdmlkZXIiLCJwcm9wcyIsInZhbHVlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nclass ApiClient {\n    async request(endpoint, options) {\n        const url = `${API_BASE_URL}${endpoint}`;\n        const response = await fetch(url, {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(`API Error: ${response.status} ${response.statusText}`);\n        }\n        return response.json();\n    }\n    // Portfolio endpoints\n    async getPortfolio() {\n        return this.request('/dashboard/portfolio');\n    }\n    async getDashboardAnalytics() {\n        return this.request('/dashboard/analytics');\n    }\n    // MSME endpoints\n    async getMSME(msmeId) {\n        return this.request(`/msme/${msmeId}`);\n    }\n    async createMSME(data) {\n        return this.request('/msme/', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async getMSMEScore(msmeId) {\n        return this.request(`/msme/${msmeId}/score`);\n    }\n    // Signal endpoints\n    async getMSMESignals(msmeId, source) {\n        const params = source ? `?source=${source}` : '';\n        return this.request(`/api/signals/${msmeId}${params}`);\n    }\n    async addSignal(msmeId, signalData) {\n        return this.request(`/msme/${msmeId}/signals`, {\n            method: 'POST',\n            body: JSON.stringify(signalData)\n        });\n    }\n    // Nudge endpoints\n    async getMSMENudges(msmeId, status) {\n        const params = status ? `?status_filter=${status}` : '';\n        return this.request(`/api/nudges/${msmeId}${params}`);\n    }\n    async sendNudge(msmeId, nudgeData) {\n        return this.request(`/msme/${msmeId}/nudge`, {\n            method: 'POST',\n            body: JSON.stringify(nudgeData)\n        });\n    }\n    // Health check\n    async healthCheck() {\n        return this.request('/health');\n    }\n}\nconst apiClient = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   formatScore: () => (/* binding */ formatScore),\n/* harmony export */   getBusinessTypeIcon: () => (/* binding */ getBusinessTypeIcon),\n/* harmony export */   getRiskBandBg: () => (/* binding */ getRiskBandBg),\n/* harmony export */   getRiskBandColor: () => (/* binding */ getRiskBandColor),\n/* harmony export */   getRiskBandColorScheme: () => (/* binding */ getRiskBandColorScheme),\n/* harmony export */   getSignalSourceIcon: () => (/* binding */ getSignalSourceIcon),\n/* harmony export */   getTrendColor: () => (/* binding */ getTrendColor),\n/* harmony export */   getTrendIcon: () => (/* binding */ getTrendIcon)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/formatDistanceToNow.js\");\n\nfunction getRiskBandColor(riskBand) {\n    switch(riskBand){\n        case 'green':\n            return 'success.600';\n        case 'yellow':\n            return 'warning.600';\n        case 'red':\n            return 'danger.600';\n        default:\n            return 'neutral.500';\n    }\n}\nfunction getRiskBandColorScheme(riskBand) {\n    switch(riskBand){\n        case 'green':\n            return 'success';\n        case 'yellow':\n            return 'warning';\n        case 'red':\n            return 'danger';\n        default:\n            return 'neutral';\n    }\n}\nfunction getRiskBandBg(riskBand) {\n    switch(riskBand){\n        case 'green':\n            return 'success.50';\n        case 'yellow':\n            return 'warning.50';\n        case 'red':\n            return 'danger.50';\n        default:\n            return 'neutral.50';\n    }\n}\nfunction formatScore(score) {\n    return Math.round(score).toString();\n}\nfunction formatDate(dateString) {\n    try {\n        const date = (0,_barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(dateString);\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.format)(date, 'MMM dd, yyyy');\n    } catch  {\n        return 'Invalid date';\n    }\n}\nfunction formatDateTime(dateString) {\n    try {\n        const date = (0,_barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(dateString);\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.format)(date, 'MMM dd, yyyy HH:mm');\n    } catch  {\n        return 'Invalid date';\n    }\n}\nfunction formatRelativeTime(dateString) {\n    try {\n        const date = (0,_barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(dateString);\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.formatDistanceToNow)(date, {\n            addSuffix: true\n        });\n    } catch  {\n        return 'Invalid date';\n    }\n}\n// Business type icon mapping for modern UI\nfunction getBusinessTypeIcon(businessType) {\n    switch(businessType){\n        case 'retail':\n            return 'Store';\n        case 'b2b':\n            return 'Building';\n        case 'services':\n            return 'Tool';\n        case 'manufacturing':\n            return 'Factory';\n        default:\n            return 'Store';\n    }\n}\n// Signal source icon mapping for modern UI\nfunction getSignalSourceIcon(source) {\n    switch(source){\n        case 'gst':\n            return 'BarChart';\n        case 'upi':\n            return 'CreditCard';\n        case 'reviews':\n            return 'Star';\n        case 'justdial':\n            return 'Phone';\n        case 'instagram':\n            return 'Camera';\n        case 'maps':\n            return 'MapPin';\n        default:\n            return 'TrendingUp';\n    }\n}\n// Trend icon mapping for modern UI\nfunction getTrendIcon(trend) {\n    switch(trend){\n        case 'improving':\n            return 'TrendingUp';\n        case 'declining':\n            return 'TrendingDown';\n        case 'stable':\n            return 'Minus';\n        default:\n            return 'Minus';\n    }\n}\nfunction getTrendColor(trend) {\n    switch(trend){\n        case 'improving':\n            return 'success.600';\n        case 'declining':\n            return 'danger.600';\n        case 'stable':\n            return 'neutral.500';\n        default:\n            return 'neutral.500';\n    }\n}\nfunction formatCurrency(amount) {\n    return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\nfunction formatNumber(num) {\n    if (num >= 10000000) {\n        return `${(num / 10000000).toFixed(1)}Cr`;\n    } else if (num >= 100000) {\n        return `${(num / 100000).toFixed(1)}L`;\n    } else if (num >= 1000) {\n        return `${(num / 1000).toFixed(1)}K`;\n    }\n    return num.toString();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/react-icons","vendor-chunks/next","vendor-chunks/@zag-js","vendor-chunks/@chakra-ui","vendor-chunks/date-fns","vendor-chunks/@emotion","vendor-chunks/@floating-ui","vendor-chunks/lucide-react","vendor-chunks/uqr","vendor-chunks/@internationalized","vendor-chunks/stylis","vendor-chunks/proxy-compare","vendor-chunks/@pandacss","vendor-chunks/react-is","vendor-chunks/next-themes","vendor-chunks/fast-safe-stringify","vendor-chunks/@ark-ui","vendor-chunks/hoist-non-react-statics","vendor-chunks/@swc","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();