"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/proxy-compare";
exports.ids = ["vendor-chunks/proxy-compare"];
exports.modules = {

/***/ "(ssr)/./node_modules/proxy-compare/dist/index.js":
/*!**************************************************!*\
  !*** ./node_modules/proxy-compare/dist/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   affectedToPathList: () => (/* binding */ affectedToPathList),\n/* harmony export */   createProxy: () => (/* binding */ createProxy),\n/* harmony export */   getUntracked: () => (/* binding */ getUntracked),\n/* harmony export */   isChanged: () => (/* binding */ isChanged),\n/* harmony export */   markToTrack: () => (/* binding */ markToTrack),\n/* harmony export */   replaceNewProxy: () => (/* binding */ replaceNewProxy),\n/* harmony export */   trackMemo: () => (/* binding */ trackMemo)\n/* harmony export */ });\n/* eslint @typescript-eslint/no-explicit-any: off */\n// symbols\nconst TRACK_MEMO_SYMBOL = Symbol();\nconst GET_ORIGINAL_SYMBOL = Symbol();\n// properties\nconst AFFECTED_PROPERTY = 'a';\nconst IS_TARGET_COPIED_PROPERTY = 'f';\nconst PROXY_PROPERTY = 'p';\nconst PROXY_CACHE_PROPERTY = 'c';\nconst TARGET_CACHE_PROPERTY = 't';\nconst HAS_KEY_PROPERTY = 'h';\nconst ALL_OWN_KEYS_PROPERTY = 'w';\nconst HAS_OWN_KEY_PROPERTY = 'o';\nconst KEYS_PROPERTY = 'k';\n// function to create a new bare proxy\nlet newProxy = (target, handler) => new Proxy(target, handler);\n// get object prototype\nconst getProto = Object.getPrototypeOf;\nconst objectsToTrack = new WeakMap();\n// check if obj is a plain object or an array\nconst isObjectToTrack = (obj) => obj &&\n    (objectsToTrack.has(obj)\n        ? objectsToTrack.get(obj)\n        : getProto(obj) === Object.prototype || getProto(obj) === Array.prototype);\n// check if it is object\nconst isObject = (x) => typeof x === 'object' && x !== null;\n// Properties that are both non-configurable and non-writable will break\n// the proxy get trap when we try to return a recursive/child compare proxy\n// from them. We can avoid this by making a copy of the target object with\n// all descriptors marked as configurable, see `copyTargetObject`.\n// See: https://github.com/dai-shi/proxy-compare/pull/8\nconst needsToCopyTargetObject = (obj) => Object.values(Object.getOwnPropertyDescriptors(obj)).some((descriptor) => !descriptor.configurable && !descriptor.writable);\n// Make a copy with all descriptors marked as configurable.\nconst copyTargetObject = (obj) => {\n    if (Array.isArray(obj)) {\n        // Arrays need a special way to copy\n        return Array.from(obj);\n    }\n    // For non-array objects, we create a new object keeping the prototype\n    // with changing all configurable options (otherwise, proxies will complain)\n    const descriptors = Object.getOwnPropertyDescriptors(obj);\n    Object.values(descriptors).forEach((desc) => {\n        desc.configurable = true;\n    });\n    return Object.create(getProto(obj), descriptors);\n};\nconst createProxyHandler = (origObj, isTargetCopied) => {\n    const state = {\n        [IS_TARGET_COPIED_PROPERTY]: isTargetCopied,\n    };\n    let trackObject = false; // for trackMemo\n    const recordUsage = (type, key) => {\n        if (!trackObject) {\n            let used = state[AFFECTED_PROPERTY].get(origObj);\n            if (!used) {\n                used = {};\n                state[AFFECTED_PROPERTY].set(origObj, used);\n            }\n            if (type === ALL_OWN_KEYS_PROPERTY) {\n                used[ALL_OWN_KEYS_PROPERTY] = true;\n            }\n            else {\n                let set = used[type];\n                if (!set) {\n                    set = new Set();\n                    used[type] = set;\n                }\n                set.add(key);\n            }\n        }\n    };\n    const recordObjectAsUsed = () => {\n        trackObject = true;\n        state[AFFECTED_PROPERTY].delete(origObj);\n    };\n    const handler = {\n        get(target, key) {\n            if (key === GET_ORIGINAL_SYMBOL) {\n                return origObj;\n            }\n            recordUsage(KEYS_PROPERTY, key);\n            return createProxy(Reflect.get(target, key), state[AFFECTED_PROPERTY], state[PROXY_CACHE_PROPERTY], state[TARGET_CACHE_PROPERTY]);\n        },\n        has(target, key) {\n            if (key === TRACK_MEMO_SYMBOL) {\n                recordObjectAsUsed();\n                return true;\n            }\n            recordUsage(HAS_KEY_PROPERTY, key);\n            return Reflect.has(target, key);\n        },\n        getOwnPropertyDescriptor(target, key) {\n            recordUsage(HAS_OWN_KEY_PROPERTY, key);\n            return Reflect.getOwnPropertyDescriptor(target, key);\n        },\n        ownKeys(target) {\n            recordUsage(ALL_OWN_KEYS_PROPERTY);\n            return Reflect.ownKeys(target);\n        },\n    };\n    if (isTargetCopied) {\n        handler.set = handler.deleteProperty = () => false;\n    }\n    return [handler, state];\n};\nconst getOriginalObject = (obj) => \n// unwrap proxy\nobj[GET_ORIGINAL_SYMBOL] ||\n    // otherwise\n    obj;\n/**\n * Create a proxy.\n *\n * This function will create a proxy at top level and proxy nested objects as you access them,\n * in order to keep track of which properties were accessed via get/has proxy handlers:\n *\n * NOTE: Printing of WeakMap is hard to inspect and not very readable\n * for this purpose you can use the `affectedToPathList` helper.\n *\n * @param {object} obj - Object that will be wrapped on the proxy.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that will hold the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [proxyCache] -\n * WeakMap that will help keep referential identity for proxies.\n * @returns {Proxy<object>} - Object wrapped in a proxy.\n *\n * @example\n * import { createProxy } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n * const proxy = createProxy(original, affected);\n *\n * proxy.a // Will mark as used and track its value.\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"a\"\n *\n * proxy.d // Will mark \"d\" as accessed to track and proxy itself ({ e: \"3\" }).\n * // This will update the affected WeakMap with original as key\n * // and a Set with \"d\"\n */\nconst createProxy = (obj, affected, proxyCache, targetCache) => {\n    if (!isObjectToTrack(obj))\n        return obj;\n    let targetAndCopied = targetCache && targetCache.get(obj);\n    if (!targetAndCopied) {\n        const target = getOriginalObject(obj);\n        if (needsToCopyTargetObject(target)) {\n            targetAndCopied = [target, copyTargetObject(target)];\n        }\n        else {\n            targetAndCopied = [target];\n        }\n        targetCache === null || targetCache === void 0 ? void 0 : targetCache.set(obj, targetAndCopied);\n    }\n    const [target, copiedTarget] = targetAndCopied;\n    let handlerAndState = proxyCache && proxyCache.get(target);\n    if (!handlerAndState ||\n        handlerAndState[1][IS_TARGET_COPIED_PROPERTY] !== !!copiedTarget) {\n        handlerAndState = createProxyHandler(target, !!copiedTarget);\n        handlerAndState[1][PROXY_PROPERTY] = newProxy(copiedTarget || target, handlerAndState[0]);\n        if (proxyCache) {\n            proxyCache.set(target, handlerAndState);\n        }\n    }\n    handlerAndState[1][AFFECTED_PROPERTY] = affected;\n    handlerAndState[1][PROXY_CACHE_PROPERTY] = proxyCache;\n    handlerAndState[1][TARGET_CACHE_PROPERTY] = targetCache;\n    return handlerAndState[1][PROXY_PROPERTY];\n};\nconst isAllOwnKeysChanged = (prevObj, nextObj) => {\n    const prevKeys = Reflect.ownKeys(prevObj);\n    const nextKeys = Reflect.ownKeys(nextObj);\n    return (prevKeys.length !== nextKeys.length ||\n        prevKeys.some((k, i) => k !== nextKeys[i]));\n};\n/**\n * Compare changes on objects.\n *\n * This will compare the affected properties on tracked objects inside the proxy\n * to check if there were any changes made to it,\n * by default if no property was accessed on the proxy it will attempt to do a\n * reference equality check for the objects provided (Object.is(a, b)). If you access a property\n * on the proxy, then isChanged will only compare the affected properties.\n *\n * @param {object} prevObj - The previous object to compare.\n * @param {object} nextObj - Object to compare with the previous one.\n * @param {WeakMap<object, unknown>} affected -\n * WeakMap that holds the tracking of which properties in the proxied object were accessed.\n * @param {WeakMap<object, unknown>} [cache] -\n * WeakMap that holds a cache of the comparisons for better performance with repetitive comparisons,\n * and to avoid infinite loop with circular structures.\n * @returns {boolean} - Boolean indicating if the affected property on the object has changed.\n *\n * @example\n * import { createProxy, isChanged } from 'proxy-compare';\n *\n * const obj = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(obj, affected);\n *\n * proxy.a\n *\n * isChanged(obj, { a: \"1\" }, affected) // false\n *\n * proxy.a = \"2\"\n *\n * isChanged(obj, { a: \"1\" }, affected) // true\n */\nconst isChanged = (prevObj, nextObj, affected, cache, // for object with cycles\nisEqual = Object.is) => {\n    if (isEqual(prevObj, nextObj)) {\n        return false;\n    }\n    if (!isObject(prevObj) || !isObject(nextObj))\n        return true;\n    const used = affected.get(getOriginalObject(prevObj));\n    if (!used)\n        return true;\n    if (cache) {\n        const hit = cache.get(prevObj);\n        if (hit === nextObj) {\n            return false;\n        }\n        // for object with cycles\n        cache.set(prevObj, nextObj);\n    }\n    let changed = null;\n    for (const key of used[HAS_KEY_PROPERTY] || []) {\n        changed = Reflect.has(prevObj, key) !== Reflect.has(nextObj, key);\n        if (changed)\n            return changed;\n    }\n    if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n        changed = isAllOwnKeysChanged(prevObj, nextObj);\n        if (changed)\n            return changed;\n    }\n    else {\n        for (const key of used[HAS_OWN_KEY_PROPERTY] || []) {\n            const hasPrev = !!Reflect.getOwnPropertyDescriptor(prevObj, key);\n            const hasNext = !!Reflect.getOwnPropertyDescriptor(nextObj, key);\n            changed = hasPrev !== hasNext;\n            if (changed)\n                return changed;\n        }\n    }\n    for (const key of used[KEYS_PROPERTY] || []) {\n        changed = isChanged(prevObj[key], nextObj[key], affected, cache, isEqual);\n        if (changed)\n            return changed;\n    }\n    if (changed === null)\n        throw new Error('invalid used');\n    return changed;\n};\n// explicitly track object with memo\nconst trackMemo = (obj) => {\n    if (isObjectToTrack(obj)) {\n        return TRACK_MEMO_SYMBOL in obj;\n    }\n    return false;\n};\n/**\n * Unwrap proxy to get the original object.\n *\n * Used to retrieve the original object used to create the proxy instance with `createProxy`.\n *\n * @param {Proxy<object>} obj -  The proxy wrapper of the originial object.\n * @returns {object | null} - Return either the unwrapped object if exists.\n *\n * @example\n * import { createProxy, getUntracked } from 'proxy-compare';\n *\n * const original = { a: \"1\", c: \"2\", d: { e: \"3\" } };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n * const originalFromProxy = getUntracked(proxy)\n *\n * Object.is(original, originalFromProxy) // true\n * isChanged(original, originalFromProxy, affected) // false\n */\nconst getUntracked = (obj) => {\n    if (isObjectToTrack(obj)) {\n        return obj[GET_ORIGINAL_SYMBOL] || null;\n    }\n    return null;\n};\n/**\n * Mark object to be tracked.\n *\n * This function marks an object that will be passed into `createProxy`\n * as marked to track or not. By default only Array and Object are marked to track,\n * so this is useful for example to mark a class instance to track or to mark a object\n * to be untracked when creating your proxy.\n *\n * @param obj - Object to mark as tracked or not.\n * @param mark - Boolean indicating whether you want to track this object or not.\n * @returns - No return.\n *\n * @example\n * import { createProxy, markToTrack, isChanged } from 'proxy-compare';\n *\n * const nested = { e: \"3\" }\n *\n * markToTrack(nested, false)\n *\n * const original = { a: \"1\", c: \"2\", d: nested };\n * const affected = new WeakMap();\n *\n * const proxy = createProxy(original, affected);\n *\n * proxy.d.e\n *\n * isChanged(original, { d: { e: \"3\" } }, affected) // true\n */\nconst markToTrack = (obj, mark = true) => {\n    objectsToTrack.set(obj, mark);\n};\n/**\n * Convert `affected` to path list\n *\n * `affected` is a weak map which is not printable.\n * This function is can convert it to printable path list.\n * It's for debugging purpose.\n *\n * @param obj - An object that is used with `createProxy`.\n * @param affected - A weak map that is used with `createProxy`.\n * @param onlyWithValues - An optional boolean to exclude object getters.\n * @returns - An array of paths.\n */\nconst affectedToPathList = (obj, affected, onlyWithValues) => {\n    const list = [];\n    const seen = new WeakSet();\n    const walk = (x, path) => {\n        var _a, _b, _c;\n        if (seen.has(x)) {\n            // for object with cycles\n            return;\n        }\n        if (isObject(x)) {\n            seen.add(x);\n        }\n        const used = isObject(x) && affected.get(getOriginalObject(x));\n        if (used) {\n            (_a = used[HAS_KEY_PROPERTY]) === null || _a === void 0 ? void 0 : _a.forEach((key) => {\n                const segment = `:has(${String(key)})`;\n                list.push(path ? [...path, segment] : [segment]);\n            });\n            if (used[ALL_OWN_KEYS_PROPERTY] === true) {\n                const segment = ':ownKeys';\n                list.push(path ? [...path, segment] : [segment]);\n            }\n            else {\n                (_b = used[HAS_OWN_KEY_PROPERTY]) === null || _b === void 0 ? void 0 : _b.forEach((key) => {\n                    const segment = `:hasOwn(${String(key)})`;\n                    list.push(path ? [...path, segment] : [segment]);\n                });\n            }\n            (_c = used[KEYS_PROPERTY]) === null || _c === void 0 ? void 0 : _c.forEach((key) => {\n                if (!onlyWithValues ||\n                    'value' in (Object.getOwnPropertyDescriptor(x, key) || {})) {\n                    walk(x[key], path ? [...path, key] : [key]);\n                }\n            });\n        }\n        else if (path) {\n            list.push(path);\n        }\n    };\n    walk(obj);\n    return list;\n};\n/**\n * replace newProxy function.\n *\n * This can be used if you want to use proxy-polyfill.\n * Note that proxy-polyfill can't polyfill everything.\n * Use it at your own risk.\n */\nconst replaceNewProxy = (fn) => {\n    newProxy = fn;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcHJveHktY29tcGFyZS9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsMEJBQTBCO0FBQ3JDO0FBQ0EsV0FBVywwQkFBMEI7QUFDckM7QUFDQSxhQUFhLGVBQWU7QUFDNUI7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQjtBQUNBLHNCQUFzQixxQkFBcUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxRUFBcUUsUUFBUTtBQUM3RTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLDBCQUEwQjtBQUNyQztBQUNBLFdBQVcsMEJBQTBCO0FBQ3JDO0FBQ0E7QUFDQSxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBLFlBQVkseUJBQXlCO0FBQ3JDO0FBQ0EsaUJBQWlCLHFCQUFxQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsUUFBUTtBQUM1QjtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsUUFBUTtBQUM1QjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGVBQWU7QUFDMUIsYUFBYSxlQUFlO0FBQzVCO0FBQ0E7QUFDQSxZQUFZLDRCQUE0QjtBQUN4QztBQUNBLHNCQUFzQixxQkFBcUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxzQ0FBc0M7QUFDbEQ7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixLQUFLLFVBQVU7QUFDeEM7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsWUFBWTtBQUNwRDtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsWUFBWTtBQUMzRDtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSw2RUFBNkU7QUFDN0U7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9tYW5pa3VtYXJnb3VuaS9Eb2N1bWVudHMvQ3JlZGl0LUNoYWtyYS1GaW5hbC9jcmVkaXQtY2hha3JhL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9wcm94eS1jb21wYXJlL2Rpc3QvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50IEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnk6IG9mZiAqL1xuLy8gc3ltYm9sc1xuY29uc3QgVFJBQ0tfTUVNT19TWU1CT0wgPSBTeW1ib2woKTtcbmNvbnN0IEdFVF9PUklHSU5BTF9TWU1CT0wgPSBTeW1ib2woKTtcbi8vIHByb3BlcnRpZXNcbmNvbnN0IEFGRkVDVEVEX1BST1BFUlRZID0gJ2EnO1xuY29uc3QgSVNfVEFSR0VUX0NPUElFRF9QUk9QRVJUWSA9ICdmJztcbmNvbnN0IFBST1hZX1BST1BFUlRZID0gJ3AnO1xuY29uc3QgUFJPWFlfQ0FDSEVfUFJPUEVSVFkgPSAnYyc7XG5jb25zdCBUQVJHRVRfQ0FDSEVfUFJPUEVSVFkgPSAndCc7XG5jb25zdCBIQVNfS0VZX1BST1BFUlRZID0gJ2gnO1xuY29uc3QgQUxMX09XTl9LRVlTX1BST1BFUlRZID0gJ3cnO1xuY29uc3QgSEFTX09XTl9LRVlfUFJPUEVSVFkgPSAnbyc7XG5jb25zdCBLRVlTX1BST1BFUlRZID0gJ2snO1xuLy8gZnVuY3Rpb24gdG8gY3JlYXRlIGEgbmV3IGJhcmUgcHJveHlcbmxldCBuZXdQcm94eSA9ICh0YXJnZXQsIGhhbmRsZXIpID0+IG5ldyBQcm94eSh0YXJnZXQsIGhhbmRsZXIpO1xuLy8gZ2V0IG9iamVjdCBwcm90b3R5cGVcbmNvbnN0IGdldFByb3RvID0gT2JqZWN0LmdldFByb3RvdHlwZU9mO1xuY29uc3Qgb2JqZWN0c1RvVHJhY2sgPSBuZXcgV2Vha01hcCgpO1xuLy8gY2hlY2sgaWYgb2JqIGlzIGEgcGxhaW4gb2JqZWN0IG9yIGFuIGFycmF5XG5jb25zdCBpc09iamVjdFRvVHJhY2sgPSAob2JqKSA9PiBvYmogJiZcbiAgICAob2JqZWN0c1RvVHJhY2suaGFzKG9iailcbiAgICAgICAgPyBvYmplY3RzVG9UcmFjay5nZXQob2JqKVxuICAgICAgICA6IGdldFByb3RvKG9iaikgPT09IE9iamVjdC5wcm90b3R5cGUgfHwgZ2V0UHJvdG8ob2JqKSA9PT0gQXJyYXkucHJvdG90eXBlKTtcbi8vIGNoZWNrIGlmIGl0IGlzIG9iamVjdFxuY29uc3QgaXNPYmplY3QgPSAoeCkgPT4gdHlwZW9mIHggPT09ICdvYmplY3QnICYmIHggIT09IG51bGw7XG4vLyBQcm9wZXJ0aWVzIHRoYXQgYXJlIGJvdGggbm9uLWNvbmZpZ3VyYWJsZSBhbmQgbm9uLXdyaXRhYmxlIHdpbGwgYnJlYWtcbi8vIHRoZSBwcm94eSBnZXQgdHJhcCB3aGVuIHdlIHRyeSB0byByZXR1cm4gYSByZWN1cnNpdmUvY2hpbGQgY29tcGFyZSBwcm94eVxuLy8gZnJvbSB0aGVtLiBXZSBjYW4gYXZvaWQgdGhpcyBieSBtYWtpbmcgYSBjb3B5IG9mIHRoZSB0YXJnZXQgb2JqZWN0IHdpdGhcbi8vIGFsbCBkZXNjcmlwdG9ycyBtYXJrZWQgYXMgY29uZmlndXJhYmxlLCBzZWUgYGNvcHlUYXJnZXRPYmplY3RgLlxuLy8gU2VlOiBodHRwczovL2dpdGh1Yi5jb20vZGFpLXNoaS9wcm94eS1jb21wYXJlL3B1bGwvOFxuY29uc3QgbmVlZHNUb0NvcHlUYXJnZXRPYmplY3QgPSAob2JqKSA9PiBPYmplY3QudmFsdWVzKE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKG9iaikpLnNvbWUoKGRlc2NyaXB0b3IpID0+ICFkZXNjcmlwdG9yLmNvbmZpZ3VyYWJsZSAmJiAhZGVzY3JpcHRvci53cml0YWJsZSk7XG4vLyBNYWtlIGEgY29weSB3aXRoIGFsbCBkZXNjcmlwdG9ycyBtYXJrZWQgYXMgY29uZmlndXJhYmxlLlxuY29uc3QgY29weVRhcmdldE9iamVjdCA9IChvYmopID0+IHtcbiAgICBpZiAoQXJyYXkuaXNBcnJheShvYmopKSB7XG4gICAgICAgIC8vIEFycmF5cyBuZWVkIGEgc3BlY2lhbCB3YXkgdG8gY29weVxuICAgICAgICByZXR1cm4gQXJyYXkuZnJvbShvYmopO1xuICAgIH1cbiAgICAvLyBGb3Igbm9uLWFycmF5IG9iamVjdHMsIHdlIGNyZWF0ZSBhIG5ldyBvYmplY3Qga2VlcGluZyB0aGUgcHJvdG90eXBlXG4gICAgLy8gd2l0aCBjaGFuZ2luZyBhbGwgY29uZmlndXJhYmxlIG9wdGlvbnMgKG90aGVyd2lzZSwgcHJveGllcyB3aWxsIGNvbXBsYWluKVxuICAgIGNvbnN0IGRlc2NyaXB0b3JzID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnMob2JqKTtcbiAgICBPYmplY3QudmFsdWVzKGRlc2NyaXB0b3JzKS5mb3JFYWNoKChkZXNjKSA9PiB7XG4gICAgICAgIGRlc2MuY29uZmlndXJhYmxlID0gdHJ1ZTtcbiAgICB9KTtcbiAgICByZXR1cm4gT2JqZWN0LmNyZWF0ZShnZXRQcm90byhvYmopLCBkZXNjcmlwdG9ycyk7XG59O1xuY29uc3QgY3JlYXRlUHJveHlIYW5kbGVyID0gKG9yaWdPYmosIGlzVGFyZ2V0Q29waWVkKSA9PiB7XG4gICAgY29uc3Qgc3RhdGUgPSB7XG4gICAgICAgIFtJU19UQVJHRVRfQ09QSUVEX1BST1BFUlRZXTogaXNUYXJnZXRDb3BpZWQsXG4gICAgfTtcbiAgICBsZXQgdHJhY2tPYmplY3QgPSBmYWxzZTsgLy8gZm9yIHRyYWNrTWVtb1xuICAgIGNvbnN0IHJlY29yZFVzYWdlID0gKHR5cGUsIGtleSkgPT4ge1xuICAgICAgICBpZiAoIXRyYWNrT2JqZWN0KSB7XG4gICAgICAgICAgICBsZXQgdXNlZCA9IHN0YXRlW0FGRkVDVEVEX1BST1BFUlRZXS5nZXQob3JpZ09iaik7XG4gICAgICAgICAgICBpZiAoIXVzZWQpIHtcbiAgICAgICAgICAgICAgICB1c2VkID0ge307XG4gICAgICAgICAgICAgICAgc3RhdGVbQUZGRUNURURfUFJPUEVSVFldLnNldChvcmlnT2JqLCB1c2VkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0eXBlID09PSBBTExfT1dOX0tFWVNfUFJPUEVSVFkpIHtcbiAgICAgICAgICAgICAgICB1c2VkW0FMTF9PV05fS0VZU19QUk9QRVJUWV0gPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgbGV0IHNldCA9IHVzZWRbdHlwZV07XG4gICAgICAgICAgICAgICAgaWYgKCFzZXQpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0ID0gbmV3IFNldCgpO1xuICAgICAgICAgICAgICAgICAgICB1c2VkW3R5cGVdID0gc2V0O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBzZXQuYWRkKGtleSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHJlY29yZE9iamVjdEFzVXNlZCA9ICgpID0+IHtcbiAgICAgICAgdHJhY2tPYmplY3QgPSB0cnVlO1xuICAgICAgICBzdGF0ZVtBRkZFQ1RFRF9QUk9QRVJUWV0uZGVsZXRlKG9yaWdPYmopO1xuICAgIH07XG4gICAgY29uc3QgaGFuZGxlciA9IHtcbiAgICAgICAgZ2V0KHRhcmdldCwga2V5KSB7XG4gICAgICAgICAgICBpZiAoa2V5ID09PSBHRVRfT1JJR0lOQUxfU1lNQk9MKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdPYmo7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZWNvcmRVc2FnZShLRVlTX1BST1BFUlRZLCBrZXkpO1xuICAgICAgICAgICAgcmV0dXJuIGNyZWF0ZVByb3h5KFJlZmxlY3QuZ2V0KHRhcmdldCwga2V5KSwgc3RhdGVbQUZGRUNURURfUFJPUEVSVFldLCBzdGF0ZVtQUk9YWV9DQUNIRV9QUk9QRVJUWV0sIHN0YXRlW1RBUkdFVF9DQUNIRV9QUk9QRVJUWV0pO1xuICAgICAgICB9LFxuICAgICAgICBoYXModGFyZ2V0LCBrZXkpIHtcbiAgICAgICAgICAgIGlmIChrZXkgPT09IFRSQUNLX01FTU9fU1lNQk9MKSB7XG4gICAgICAgICAgICAgICAgcmVjb3JkT2JqZWN0QXNVc2VkKCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZWNvcmRVc2FnZShIQVNfS0VZX1BST1BFUlRZLCBrZXkpO1xuICAgICAgICAgICAgcmV0dXJuIFJlZmxlY3QuaGFzKHRhcmdldCwga2V5KTtcbiAgICAgICAgfSxcbiAgICAgICAgZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHRhcmdldCwga2V5KSB7XG4gICAgICAgICAgICByZWNvcmRVc2FnZShIQVNfT1dOX0tFWV9QUk9QRVJUWSwga2V5KTtcbiAgICAgICAgICAgIHJldHVybiBSZWZsZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcih0YXJnZXQsIGtleSk7XG4gICAgICAgIH0sXG4gICAgICAgIG93bktleXModGFyZ2V0KSB7XG4gICAgICAgICAgICByZWNvcmRVc2FnZShBTExfT1dOX0tFWVNfUFJPUEVSVFkpO1xuICAgICAgICAgICAgcmV0dXJuIFJlZmxlY3Qub3duS2V5cyh0YXJnZXQpO1xuICAgICAgICB9LFxuICAgIH07XG4gICAgaWYgKGlzVGFyZ2V0Q29waWVkKSB7XG4gICAgICAgIGhhbmRsZXIuc2V0ID0gaGFuZGxlci5kZWxldGVQcm9wZXJ0eSA9ICgpID0+IGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gW2hhbmRsZXIsIHN0YXRlXTtcbn07XG5jb25zdCBnZXRPcmlnaW5hbE9iamVjdCA9IChvYmopID0+IFxuLy8gdW53cmFwIHByb3h5XG5vYmpbR0VUX09SSUdJTkFMX1NZTUJPTF0gfHxcbiAgICAvLyBvdGhlcndpc2VcbiAgICBvYmo7XG4vKipcbiAqIENyZWF0ZSBhIHByb3h5LlxuICpcbiAqIFRoaXMgZnVuY3Rpb24gd2lsbCBjcmVhdGUgYSBwcm94eSBhdCB0b3AgbGV2ZWwgYW5kIHByb3h5IG5lc3RlZCBvYmplY3RzIGFzIHlvdSBhY2Nlc3MgdGhlbSxcbiAqIGluIG9yZGVyIHRvIGtlZXAgdHJhY2sgb2Ygd2hpY2ggcHJvcGVydGllcyB3ZXJlIGFjY2Vzc2VkIHZpYSBnZXQvaGFzIHByb3h5IGhhbmRsZXJzOlxuICpcbiAqIE5PVEU6IFByaW50aW5nIG9mIFdlYWtNYXAgaXMgaGFyZCB0byBpbnNwZWN0IGFuZCBub3QgdmVyeSByZWFkYWJsZVxuICogZm9yIHRoaXMgcHVycG9zZSB5b3UgY2FuIHVzZSB0aGUgYGFmZmVjdGVkVG9QYXRoTGlzdGAgaGVscGVyLlxuICpcbiAqIEBwYXJhbSB7b2JqZWN0fSBvYmogLSBPYmplY3QgdGhhdCB3aWxsIGJlIHdyYXBwZWQgb24gdGhlIHByb3h5LlxuICogQHBhcmFtIHtXZWFrTWFwPG9iamVjdCwgdW5rbm93bj59IGFmZmVjdGVkIC1cbiAqIFdlYWtNYXAgdGhhdCB3aWxsIGhvbGQgdGhlIHRyYWNraW5nIG9mIHdoaWNoIHByb3BlcnRpZXMgaW4gdGhlIHByb3hpZWQgb2JqZWN0IHdlcmUgYWNjZXNzZWQuXG4gKiBAcGFyYW0ge1dlYWtNYXA8b2JqZWN0LCB1bmtub3duPn0gW3Byb3h5Q2FjaGVdIC1cbiAqIFdlYWtNYXAgdGhhdCB3aWxsIGhlbHAga2VlcCByZWZlcmVudGlhbCBpZGVudGl0eSBmb3IgcHJveGllcy5cbiAqIEByZXR1cm5zIHtQcm94eTxvYmplY3Q+fSAtIE9iamVjdCB3cmFwcGVkIGluIGEgcHJveHkuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IGNyZWF0ZVByb3h5IH0gZnJvbSAncHJveHktY29tcGFyZSc7XG4gKlxuICogY29uc3Qgb3JpZ2luYWwgPSB7IGE6IFwiMVwiLCBjOiBcIjJcIiwgZDogeyBlOiBcIjNcIiB9IH07XG4gKiBjb25zdCBhZmZlY3RlZCA9IG5ldyBXZWFrTWFwKCk7XG4gKiBjb25zdCBwcm94eSA9IGNyZWF0ZVByb3h5KG9yaWdpbmFsLCBhZmZlY3RlZCk7XG4gKlxuICogcHJveHkuYSAvLyBXaWxsIG1hcmsgYXMgdXNlZCBhbmQgdHJhY2sgaXRzIHZhbHVlLlxuICogLy8gVGhpcyB3aWxsIHVwZGF0ZSB0aGUgYWZmZWN0ZWQgV2Vha01hcCB3aXRoIG9yaWdpbmFsIGFzIGtleVxuICogLy8gYW5kIGEgU2V0IHdpdGggXCJhXCJcbiAqXG4gKiBwcm94eS5kIC8vIFdpbGwgbWFyayBcImRcIiBhcyBhY2Nlc3NlZCB0byB0cmFjayBhbmQgcHJveHkgaXRzZWxmICh7IGU6IFwiM1wiIH0pLlxuICogLy8gVGhpcyB3aWxsIHVwZGF0ZSB0aGUgYWZmZWN0ZWQgV2Vha01hcCB3aXRoIG9yaWdpbmFsIGFzIGtleVxuICogLy8gYW5kIGEgU2V0IHdpdGggXCJkXCJcbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZVByb3h5ID0gKG9iaiwgYWZmZWN0ZWQsIHByb3h5Q2FjaGUsIHRhcmdldENhY2hlKSA9PiB7XG4gICAgaWYgKCFpc09iamVjdFRvVHJhY2sob2JqKSlcbiAgICAgICAgcmV0dXJuIG9iajtcbiAgICBsZXQgdGFyZ2V0QW5kQ29waWVkID0gdGFyZ2V0Q2FjaGUgJiYgdGFyZ2V0Q2FjaGUuZ2V0KG9iaik7XG4gICAgaWYgKCF0YXJnZXRBbmRDb3BpZWQpIHtcbiAgICAgICAgY29uc3QgdGFyZ2V0ID0gZ2V0T3JpZ2luYWxPYmplY3Qob2JqKTtcbiAgICAgICAgaWYgKG5lZWRzVG9Db3B5VGFyZ2V0T2JqZWN0KHRhcmdldCkpIHtcbiAgICAgICAgICAgIHRhcmdldEFuZENvcGllZCA9IFt0YXJnZXQsIGNvcHlUYXJnZXRPYmplY3QodGFyZ2V0KV07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0YXJnZXRBbmRDb3BpZWQgPSBbdGFyZ2V0XTtcbiAgICAgICAgfVxuICAgICAgICB0YXJnZXRDYWNoZSA9PT0gbnVsbCB8fCB0YXJnZXRDYWNoZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogdGFyZ2V0Q2FjaGUuc2V0KG9iaiwgdGFyZ2V0QW5kQ29waWVkKTtcbiAgICB9XG4gICAgY29uc3QgW3RhcmdldCwgY29waWVkVGFyZ2V0XSA9IHRhcmdldEFuZENvcGllZDtcbiAgICBsZXQgaGFuZGxlckFuZFN0YXRlID0gcHJveHlDYWNoZSAmJiBwcm94eUNhY2hlLmdldCh0YXJnZXQpO1xuICAgIGlmICghaGFuZGxlckFuZFN0YXRlIHx8XG4gICAgICAgIGhhbmRsZXJBbmRTdGF0ZVsxXVtJU19UQVJHRVRfQ09QSUVEX1BST1BFUlRZXSAhPT0gISFjb3BpZWRUYXJnZXQpIHtcbiAgICAgICAgaGFuZGxlckFuZFN0YXRlID0gY3JlYXRlUHJveHlIYW5kbGVyKHRhcmdldCwgISFjb3BpZWRUYXJnZXQpO1xuICAgICAgICBoYW5kbGVyQW5kU3RhdGVbMV1bUFJPWFlfUFJPUEVSVFldID0gbmV3UHJveHkoY29waWVkVGFyZ2V0IHx8IHRhcmdldCwgaGFuZGxlckFuZFN0YXRlWzBdKTtcbiAgICAgICAgaWYgKHByb3h5Q2FjaGUpIHtcbiAgICAgICAgICAgIHByb3h5Q2FjaGUuc2V0KHRhcmdldCwgaGFuZGxlckFuZFN0YXRlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBoYW5kbGVyQW5kU3RhdGVbMV1bQUZGRUNURURfUFJPUEVSVFldID0gYWZmZWN0ZWQ7XG4gICAgaGFuZGxlckFuZFN0YXRlWzFdW1BST1hZX0NBQ0hFX1BST1BFUlRZXSA9IHByb3h5Q2FjaGU7XG4gICAgaGFuZGxlckFuZFN0YXRlWzFdW1RBUkdFVF9DQUNIRV9QUk9QRVJUWV0gPSB0YXJnZXRDYWNoZTtcbiAgICByZXR1cm4gaGFuZGxlckFuZFN0YXRlWzFdW1BST1hZX1BST1BFUlRZXTtcbn07XG5jb25zdCBpc0FsbE93bktleXNDaGFuZ2VkID0gKHByZXZPYmosIG5leHRPYmopID0+IHtcbiAgICBjb25zdCBwcmV2S2V5cyA9IFJlZmxlY3Qub3duS2V5cyhwcmV2T2JqKTtcbiAgICBjb25zdCBuZXh0S2V5cyA9IFJlZmxlY3Qub3duS2V5cyhuZXh0T2JqKTtcbiAgICByZXR1cm4gKHByZXZLZXlzLmxlbmd0aCAhPT0gbmV4dEtleXMubGVuZ3RoIHx8XG4gICAgICAgIHByZXZLZXlzLnNvbWUoKGssIGkpID0+IGsgIT09IG5leHRLZXlzW2ldKSk7XG59O1xuLyoqXG4gKiBDb21wYXJlIGNoYW5nZXMgb24gb2JqZWN0cy5cbiAqXG4gKiBUaGlzIHdpbGwgY29tcGFyZSB0aGUgYWZmZWN0ZWQgcHJvcGVydGllcyBvbiB0cmFja2VkIG9iamVjdHMgaW5zaWRlIHRoZSBwcm94eVxuICogdG8gY2hlY2sgaWYgdGhlcmUgd2VyZSBhbnkgY2hhbmdlcyBtYWRlIHRvIGl0LFxuICogYnkgZGVmYXVsdCBpZiBubyBwcm9wZXJ0eSB3YXMgYWNjZXNzZWQgb24gdGhlIHByb3h5IGl0IHdpbGwgYXR0ZW1wdCB0byBkbyBhXG4gKiByZWZlcmVuY2UgZXF1YWxpdHkgY2hlY2sgZm9yIHRoZSBvYmplY3RzIHByb3ZpZGVkIChPYmplY3QuaXMoYSwgYikpLiBJZiB5b3UgYWNjZXNzIGEgcHJvcGVydHlcbiAqIG9uIHRoZSBwcm94eSwgdGhlbiBpc0NoYW5nZWQgd2lsbCBvbmx5IGNvbXBhcmUgdGhlIGFmZmVjdGVkIHByb3BlcnRpZXMuXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHByZXZPYmogLSBUaGUgcHJldmlvdXMgb2JqZWN0IHRvIGNvbXBhcmUuXG4gKiBAcGFyYW0ge29iamVjdH0gbmV4dE9iaiAtIE9iamVjdCB0byBjb21wYXJlIHdpdGggdGhlIHByZXZpb3VzIG9uZS5cbiAqIEBwYXJhbSB7V2Vha01hcDxvYmplY3QsIHVua25vd24+fSBhZmZlY3RlZCAtXG4gKiBXZWFrTWFwIHRoYXQgaG9sZHMgdGhlIHRyYWNraW5nIG9mIHdoaWNoIHByb3BlcnRpZXMgaW4gdGhlIHByb3hpZWQgb2JqZWN0IHdlcmUgYWNjZXNzZWQuXG4gKiBAcGFyYW0ge1dlYWtNYXA8b2JqZWN0LCB1bmtub3duPn0gW2NhY2hlXSAtXG4gKiBXZWFrTWFwIHRoYXQgaG9sZHMgYSBjYWNoZSBvZiB0aGUgY29tcGFyaXNvbnMgZm9yIGJldHRlciBwZXJmb3JtYW5jZSB3aXRoIHJlcGV0aXRpdmUgY29tcGFyaXNvbnMsXG4gKiBhbmQgdG8gYXZvaWQgaW5maW5pdGUgbG9vcCB3aXRoIGNpcmN1bGFyIHN0cnVjdHVyZXMuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn0gLSBCb29sZWFuIGluZGljYXRpbmcgaWYgdGhlIGFmZmVjdGVkIHByb3BlcnR5IG9uIHRoZSBvYmplY3QgaGFzIGNoYW5nZWQuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IGNyZWF0ZVByb3h5LCBpc0NoYW5nZWQgfSBmcm9tICdwcm94eS1jb21wYXJlJztcbiAqXG4gKiBjb25zdCBvYmogPSB7IGE6IFwiMVwiLCBjOiBcIjJcIiwgZDogeyBlOiBcIjNcIiB9IH07XG4gKiBjb25zdCBhZmZlY3RlZCA9IG5ldyBXZWFrTWFwKCk7XG4gKlxuICogY29uc3QgcHJveHkgPSBjcmVhdGVQcm94eShvYmosIGFmZmVjdGVkKTtcbiAqXG4gKiBwcm94eS5hXG4gKlxuICogaXNDaGFuZ2VkKG9iaiwgeyBhOiBcIjFcIiB9LCBhZmZlY3RlZCkgLy8gZmFsc2VcbiAqXG4gKiBwcm94eS5hID0gXCIyXCJcbiAqXG4gKiBpc0NoYW5nZWQob2JqLCB7IGE6IFwiMVwiIH0sIGFmZmVjdGVkKSAvLyB0cnVlXG4gKi9cbmV4cG9ydCBjb25zdCBpc0NoYW5nZWQgPSAocHJldk9iaiwgbmV4dE9iaiwgYWZmZWN0ZWQsIGNhY2hlLCAvLyBmb3Igb2JqZWN0IHdpdGggY3ljbGVzXG5pc0VxdWFsID0gT2JqZWN0LmlzKSA9PiB7XG4gICAgaWYgKGlzRXF1YWwocHJldk9iaiwgbmV4dE9iaikpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoIWlzT2JqZWN0KHByZXZPYmopIHx8ICFpc09iamVjdChuZXh0T2JqKSlcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgY29uc3QgdXNlZCA9IGFmZmVjdGVkLmdldChnZXRPcmlnaW5hbE9iamVjdChwcmV2T2JqKSk7XG4gICAgaWYgKCF1c2VkKVxuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICBpZiAoY2FjaGUpIHtcbiAgICAgICAgY29uc3QgaGl0ID0gY2FjaGUuZ2V0KHByZXZPYmopO1xuICAgICAgICBpZiAoaGl0ID09PSBuZXh0T2JqKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgLy8gZm9yIG9iamVjdCB3aXRoIGN5Y2xlc1xuICAgICAgICBjYWNoZS5zZXQocHJldk9iaiwgbmV4dE9iaik7XG4gICAgfVxuICAgIGxldCBjaGFuZ2VkID0gbnVsbDtcbiAgICBmb3IgKGNvbnN0IGtleSBvZiB1c2VkW0hBU19LRVlfUFJPUEVSVFldIHx8IFtdKSB7XG4gICAgICAgIGNoYW5nZWQgPSBSZWZsZWN0LmhhcyhwcmV2T2JqLCBrZXkpICE9PSBSZWZsZWN0LmhhcyhuZXh0T2JqLCBrZXkpO1xuICAgICAgICBpZiAoY2hhbmdlZClcbiAgICAgICAgICAgIHJldHVybiBjaGFuZ2VkO1xuICAgIH1cbiAgICBpZiAodXNlZFtBTExfT1dOX0tFWVNfUFJPUEVSVFldID09PSB0cnVlKSB7XG4gICAgICAgIGNoYW5nZWQgPSBpc0FsbE93bktleXNDaGFuZ2VkKHByZXZPYmosIG5leHRPYmopO1xuICAgICAgICBpZiAoY2hhbmdlZClcbiAgICAgICAgICAgIHJldHVybiBjaGFuZ2VkO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgb2YgdXNlZFtIQVNfT1dOX0tFWV9QUk9QRVJUWV0gfHwgW10pIHtcbiAgICAgICAgICAgIGNvbnN0IGhhc1ByZXYgPSAhIVJlZmxlY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHByZXZPYmosIGtleSk7XG4gICAgICAgICAgICBjb25zdCBoYXNOZXh0ID0gISFSZWZsZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihuZXh0T2JqLCBrZXkpO1xuICAgICAgICAgICAgY2hhbmdlZCA9IGhhc1ByZXYgIT09IGhhc05leHQ7XG4gICAgICAgICAgICBpZiAoY2hhbmdlZClcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhbmdlZDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmb3IgKGNvbnN0IGtleSBvZiB1c2VkW0tFWVNfUFJPUEVSVFldIHx8IFtdKSB7XG4gICAgICAgIGNoYW5nZWQgPSBpc0NoYW5nZWQocHJldk9ialtrZXldLCBuZXh0T2JqW2tleV0sIGFmZmVjdGVkLCBjYWNoZSwgaXNFcXVhbCk7XG4gICAgICAgIGlmIChjaGFuZ2VkKVxuICAgICAgICAgICAgcmV0dXJuIGNoYW5nZWQ7XG4gICAgfVxuICAgIGlmIChjaGFuZ2VkID09PSBudWxsKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2ludmFsaWQgdXNlZCcpO1xuICAgIHJldHVybiBjaGFuZ2VkO1xufTtcbi8vIGV4cGxpY2l0bHkgdHJhY2sgb2JqZWN0IHdpdGggbWVtb1xuZXhwb3J0IGNvbnN0IHRyYWNrTWVtbyA9IChvYmopID0+IHtcbiAgICBpZiAoaXNPYmplY3RUb1RyYWNrKG9iaikpIHtcbiAgICAgICAgcmV0dXJuIFRSQUNLX01FTU9fU1lNQk9MIGluIG9iajtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufTtcbi8qKlxuICogVW53cmFwIHByb3h5IHRvIGdldCB0aGUgb3JpZ2luYWwgb2JqZWN0LlxuICpcbiAqIFVzZWQgdG8gcmV0cmlldmUgdGhlIG9yaWdpbmFsIG9iamVjdCB1c2VkIHRvIGNyZWF0ZSB0aGUgcHJveHkgaW5zdGFuY2Ugd2l0aCBgY3JlYXRlUHJveHlgLlxuICpcbiAqIEBwYXJhbSB7UHJveHk8b2JqZWN0Pn0gb2JqIC0gIFRoZSBwcm94eSB3cmFwcGVyIG9mIHRoZSBvcmlnaW5pYWwgb2JqZWN0LlxuICogQHJldHVybnMge29iamVjdCB8IG51bGx9IC0gUmV0dXJuIGVpdGhlciB0aGUgdW53cmFwcGVkIG9iamVjdCBpZiBleGlzdHMuXG4gKlxuICogQGV4YW1wbGVcbiAqIGltcG9ydCB7IGNyZWF0ZVByb3h5LCBnZXRVbnRyYWNrZWQgfSBmcm9tICdwcm94eS1jb21wYXJlJztcbiAqXG4gKiBjb25zdCBvcmlnaW5hbCA9IHsgYTogXCIxXCIsIGM6IFwiMlwiLCBkOiB7IGU6IFwiM1wiIH0gfTtcbiAqIGNvbnN0IGFmZmVjdGVkID0gbmV3IFdlYWtNYXAoKTtcbiAqXG4gKiBjb25zdCBwcm94eSA9IGNyZWF0ZVByb3h5KG9yaWdpbmFsLCBhZmZlY3RlZCk7XG4gKiBjb25zdCBvcmlnaW5hbEZyb21Qcm94eSA9IGdldFVudHJhY2tlZChwcm94eSlcbiAqXG4gKiBPYmplY3QuaXMob3JpZ2luYWwsIG9yaWdpbmFsRnJvbVByb3h5KSAvLyB0cnVlXG4gKiBpc0NoYW5nZWQob3JpZ2luYWwsIG9yaWdpbmFsRnJvbVByb3h5LCBhZmZlY3RlZCkgLy8gZmFsc2VcbiAqL1xuZXhwb3J0IGNvbnN0IGdldFVudHJhY2tlZCA9IChvYmopID0+IHtcbiAgICBpZiAoaXNPYmplY3RUb1RyYWNrKG9iaikpIHtcbiAgICAgICAgcmV0dXJuIG9ialtHRVRfT1JJR0lOQUxfU1lNQk9MXSB8fCBudWxsO1xuICAgIH1cbiAgICByZXR1cm4gbnVsbDtcbn07XG4vKipcbiAqIE1hcmsgb2JqZWN0IHRvIGJlIHRyYWNrZWQuXG4gKlxuICogVGhpcyBmdW5jdGlvbiBtYXJrcyBhbiBvYmplY3QgdGhhdCB3aWxsIGJlIHBhc3NlZCBpbnRvIGBjcmVhdGVQcm94eWBcbiAqIGFzIG1hcmtlZCB0byB0cmFjayBvciBub3QuIEJ5IGRlZmF1bHQgb25seSBBcnJheSBhbmQgT2JqZWN0IGFyZSBtYXJrZWQgdG8gdHJhY2ssXG4gKiBzbyB0aGlzIGlzIHVzZWZ1bCBmb3IgZXhhbXBsZSB0byBtYXJrIGEgY2xhc3MgaW5zdGFuY2UgdG8gdHJhY2sgb3IgdG8gbWFyayBhIG9iamVjdFxuICogdG8gYmUgdW50cmFja2VkIHdoZW4gY3JlYXRpbmcgeW91ciBwcm94eS5cbiAqXG4gKiBAcGFyYW0gb2JqIC0gT2JqZWN0IHRvIG1hcmsgYXMgdHJhY2tlZCBvciBub3QuXG4gKiBAcGFyYW0gbWFyayAtIEJvb2xlYW4gaW5kaWNhdGluZyB3aGV0aGVyIHlvdSB3YW50IHRvIHRyYWNrIHRoaXMgb2JqZWN0IG9yIG5vdC5cbiAqIEByZXR1cm5zIC0gTm8gcmV0dXJuLlxuICpcbiAqIEBleGFtcGxlXG4gKiBpbXBvcnQgeyBjcmVhdGVQcm94eSwgbWFya1RvVHJhY2ssIGlzQ2hhbmdlZCB9IGZyb20gJ3Byb3h5LWNvbXBhcmUnO1xuICpcbiAqIGNvbnN0IG5lc3RlZCA9IHsgZTogXCIzXCIgfVxuICpcbiAqIG1hcmtUb1RyYWNrKG5lc3RlZCwgZmFsc2UpXG4gKlxuICogY29uc3Qgb3JpZ2luYWwgPSB7IGE6IFwiMVwiLCBjOiBcIjJcIiwgZDogbmVzdGVkIH07XG4gKiBjb25zdCBhZmZlY3RlZCA9IG5ldyBXZWFrTWFwKCk7XG4gKlxuICogY29uc3QgcHJveHkgPSBjcmVhdGVQcm94eShvcmlnaW5hbCwgYWZmZWN0ZWQpO1xuICpcbiAqIHByb3h5LmQuZVxuICpcbiAqIGlzQ2hhbmdlZChvcmlnaW5hbCwgeyBkOiB7IGU6IFwiM1wiIH0gfSwgYWZmZWN0ZWQpIC8vIHRydWVcbiAqL1xuZXhwb3J0IGNvbnN0IG1hcmtUb1RyYWNrID0gKG9iaiwgbWFyayA9IHRydWUpID0+IHtcbiAgICBvYmplY3RzVG9UcmFjay5zZXQob2JqLCBtYXJrKTtcbn07XG4vKipcbiAqIENvbnZlcnQgYGFmZmVjdGVkYCB0byBwYXRoIGxpc3RcbiAqXG4gKiBgYWZmZWN0ZWRgIGlzIGEgd2VhayBtYXAgd2hpY2ggaXMgbm90IHByaW50YWJsZS5cbiAqIFRoaXMgZnVuY3Rpb24gaXMgY2FuIGNvbnZlcnQgaXQgdG8gcHJpbnRhYmxlIHBhdGggbGlzdC5cbiAqIEl0J3MgZm9yIGRlYnVnZ2luZyBwdXJwb3NlLlxuICpcbiAqIEBwYXJhbSBvYmogLSBBbiBvYmplY3QgdGhhdCBpcyB1c2VkIHdpdGggYGNyZWF0ZVByb3h5YC5cbiAqIEBwYXJhbSBhZmZlY3RlZCAtIEEgd2VhayBtYXAgdGhhdCBpcyB1c2VkIHdpdGggYGNyZWF0ZVByb3h5YC5cbiAqIEBwYXJhbSBvbmx5V2l0aFZhbHVlcyAtIEFuIG9wdGlvbmFsIGJvb2xlYW4gdG8gZXhjbHVkZSBvYmplY3QgZ2V0dGVycy5cbiAqIEByZXR1cm5zIC0gQW4gYXJyYXkgb2YgcGF0aHMuXG4gKi9cbmV4cG9ydCBjb25zdCBhZmZlY3RlZFRvUGF0aExpc3QgPSAob2JqLCBhZmZlY3RlZCwgb25seVdpdGhWYWx1ZXMpID0+IHtcbiAgICBjb25zdCBsaXN0ID0gW107XG4gICAgY29uc3Qgc2VlbiA9IG5ldyBXZWFrU2V0KCk7XG4gICAgY29uc3Qgd2FsayA9ICh4LCBwYXRoKSA9PiB7XG4gICAgICAgIHZhciBfYSwgX2IsIF9jO1xuICAgICAgICBpZiAoc2Vlbi5oYXMoeCkpIHtcbiAgICAgICAgICAgIC8vIGZvciBvYmplY3Qgd2l0aCBjeWNsZXNcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaXNPYmplY3QoeCkpIHtcbiAgICAgICAgICAgIHNlZW4uYWRkKHgpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHVzZWQgPSBpc09iamVjdCh4KSAmJiBhZmZlY3RlZC5nZXQoZ2V0T3JpZ2luYWxPYmplY3QoeCkpO1xuICAgICAgICBpZiAodXNlZCkge1xuICAgICAgICAgICAgKF9hID0gdXNlZFtIQVNfS0VZX1BST1BFUlRZXSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmZvckVhY2goKGtleSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHNlZ21lbnQgPSBgOmhhcygke1N0cmluZyhrZXkpfSlgO1xuICAgICAgICAgICAgICAgIGxpc3QucHVzaChwYXRoID8gWy4uLnBhdGgsIHNlZ21lbnRdIDogW3NlZ21lbnRdKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHVzZWRbQUxMX09XTl9LRVlTX1BST1BFUlRZXSA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHNlZ21lbnQgPSAnOm93bktleXMnO1xuICAgICAgICAgICAgICAgIGxpc3QucHVzaChwYXRoID8gWy4uLnBhdGgsIHNlZ21lbnRdIDogW3NlZ21lbnRdKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIChfYiA9IHVzZWRbSEFTX09XTl9LRVlfUFJPUEVSVFldKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNlZ21lbnQgPSBgOmhhc093bigke1N0cmluZyhrZXkpfSlgO1xuICAgICAgICAgICAgICAgICAgICBsaXN0LnB1c2gocGF0aCA/IFsuLi5wYXRoLCBzZWdtZW50XSA6IFtzZWdtZW50XSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAoX2MgPSB1c2VkW0tFWVNfUFJPUEVSVFldKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFvbmx5V2l0aFZhbHVlcyB8fFxuICAgICAgICAgICAgICAgICAgICAndmFsdWUnIGluIChPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHgsIGtleSkgfHwge30pKSB7XG4gICAgICAgICAgICAgICAgICAgIHdhbGsoeFtrZXldLCBwYXRoID8gWy4uLnBhdGgsIGtleV0gOiBba2V5XSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAocGF0aCkge1xuICAgICAgICAgICAgbGlzdC5wdXNoKHBhdGgpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICB3YWxrKG9iaik7XG4gICAgcmV0dXJuIGxpc3Q7XG59O1xuLyoqXG4gKiByZXBsYWNlIG5ld1Byb3h5IGZ1bmN0aW9uLlxuICpcbiAqIFRoaXMgY2FuIGJlIHVzZWQgaWYgeW91IHdhbnQgdG8gdXNlIHByb3h5LXBvbHlmaWxsLlxuICogTm90ZSB0aGF0IHByb3h5LXBvbHlmaWxsIGNhbid0IHBvbHlmaWxsIGV2ZXJ5dGhpbmcuXG4gKiBVc2UgaXQgYXQgeW91ciBvd24gcmlzay5cbiAqL1xuZXhwb3J0IGNvbnN0IHJlcGxhY2VOZXdQcm94eSA9IChmbikgPT4ge1xuICAgIG5ld1Byb3h5ID0gZm47XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/proxy-compare/dist/index.js\n");

/***/ })

};
;