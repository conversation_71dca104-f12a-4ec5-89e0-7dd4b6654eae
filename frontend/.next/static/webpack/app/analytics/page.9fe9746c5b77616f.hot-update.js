"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/stack/v-stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/spinner/spinner.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/alert/namespace.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/card/namespace.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/card/card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/stack/h-stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Progress,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/progress/namespace.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Icon */ \"(app-pages-browser)/./src/components/ui/Icon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fetchAnalytics = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching analytics data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getDashboardAnalytics();\n            console.log('Analytics data received:', data);\n            setAnalytics(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching analytics:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch analytics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPage.useEffect\": ()=>{\n            fetchAnalytics();\n        }\n    }[\"AnalyticsPage.useEffect\"], []);\n    const getRiskBandColor = (band)=>{\n        switch(band){\n            case 'green':\n                return 'success.500';\n            case 'yellow':\n                return 'warning.500';\n            case 'red':\n                return 'danger.500';\n            default:\n                return 'neutral.500';\n        }\n    };\n    const getRiskBandLabel = (band)=>{\n        switch(band){\n            case 'green':\n                return 'Low Risk';\n            case 'yellow':\n                return 'Medium Risk';\n            case 'red':\n                return 'High Risk';\n            default:\n                return band;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                        spacing: 6,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                w: 16,\n                                h: 16,\n                                bg: \"brand.100\",\n                                borderRadius: \"full\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Spinner, {\n                                    size: \"lg\",\n                                    color: \"brand.600\",\n                                    thickness: \"3px\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"600\",\n                                        color: \"neutral.700\",\n                                        children: \"Loading Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"neutral.500\",\n                                        children: \"Fetching dashboard data...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"danger.200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__, {\n                        status: \"error\",\n                        bg: \"danger.50\",\n                        borderRadius: \"lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertIcon, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                align: \"start\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontWeight: \"600\",\n                                        color: \"danger.800\",\n                                        children: \"Failed to load analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"danger.600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    if (!analytics) {\n        return null;\n    }\n    const totalRiskCount = analytics.risk_distribution.green + analytics.risk_distribution.yellow + analytics.risk_distribution.red;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                            align: \"start\",\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    children: \"Analytics Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                    color: \"neutral.600\",\n                                    fontSize: \"sm\",\n                                    children: [\n                                        \"Last updated: \",\n                                        new Date(analytics.last_updated).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                name: \"Users\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 23\n                            }, void 0),\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/portfolio'),\n                            shadow: \"sm\",\n                            _hover: {\n                                shadow: 'md',\n                                transform: 'translateY(-1px)'\n                            },\n                            children: \"View Portfolio\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        md: 2,\n                        lg: 4\n                    },\n                    spacing: 6,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                    p: 2,\n                                                    bg: \"brand.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"brand.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                        name: \"Users\",\n                                                        size: 20,\n                                                        color: \"brand.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Total MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"neutral.800\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_msmes)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                            size: \"sm\",\n                                            variant: \"ghost\",\n                                            colorScheme: \"brand\",\n                                            onClick: ()=>router.push('/portfolio'),\n                                            rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                name: \"ChevronRight\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                    p: 2,\n                                                    bg: \"info.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"info.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                        name: \"Activity\",\n                                                        size: 20,\n                                                        color: \"info.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Total Signals\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"neutral.800\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_signals)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                analytics.average_signals_per_msme.toFixed(1),\n                                                \" avg per MSME\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                    p: 2,\n                                                    bg: \"success.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"success.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                        name: \"Shield\",\n                                                        size: 20,\n                                                        color: \"success.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Low Risk MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"success.600\",\n                                                            children: analytics.risk_distribution.green\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                totalRiskCount > 0 ? (analytics.risk_distribution.green / totalRiskCount * 100).toFixed(1) : 0,\n                                                \"% of portfolio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                    p: 2,\n                                                    bg: \"danger.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"danger.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                        name: \"AlertCircle\",\n                                                        size: 20,\n                                                        color: \"danger.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"High Risk MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"danger.600\",\n                                                            children: analytics.risk_distribution.red\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                totalRiskCount > 0 ? (analytics.risk_distribution.red / totalRiskCount * 100).toFixed(1) : 0,\n                                                \"% of portfolio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        lg: 2\n                    },\n                    spacing: 6,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Risk Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.risk_distribution).map((param)=>{\n                                            let [risk, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Badge, {\n                                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                                        variant: \"subtle\",\n                                                                        borderRadius: \"full\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        fontSize: \"xs\",\n                                                                        fontWeight: \"600\",\n                                                                        children: getRiskBandLabel(risk)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    totalRiskCount > 0 ? (count / totalRiskCount * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__, {\n                                                        value: totalRiskCount > 0 ? count / totalRiskCount * 100 : 0,\n                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, risk, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Business Type Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.business_type_distribution).map((param)=>{\n                                            let [type, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                        name: type === 'retail' ? 'Store' : type === 'b2b' ? 'Building' : type === 'services' ? 'Tool' : 'Factory',\n                                                                        size: 16,\n                                                                        color: \"neutral.600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.700\",\n                                                                        textTransform: \"capitalize\",\n                                                                        fontWeight: \"500\",\n                                                                        children: type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    analytics.total_msmes > 0 ? (count / analytics.total_msmes * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Progress_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__, {\n                                                        value: analytics.total_msmes > 0 ? count / analytics.total_msmes * 100 : 0,\n                                                        colorScheme: \"brand\",\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, type, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"t507Zj7k0FGCuaLMMY/kfCFOcuI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});