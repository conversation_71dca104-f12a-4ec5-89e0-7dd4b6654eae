"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fetchAnalytics = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching analytics data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getDashboardAnalytics();\n            console.log('Analytics data received:', data);\n            setAnalytics(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching analytics:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch analytics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPage.useEffect\": ()=>{\n            fetchAnalytics();\n        }\n    }[\"AnalyticsPage.useEffect\"], []);\n    const getRiskBandColor = (band)=>{\n        switch(band){\n            case 'green':\n                return 'success.500';\n            case 'yellow':\n                return 'warning.500';\n            case 'red':\n                return 'danger.500';\n            default:\n                return 'neutral.500';\n        }\n    };\n    const getRiskBandLabel = (band)=>{\n        switch(band){\n            case 'green':\n                return 'Low Risk';\n            case 'yellow':\n                return 'Medium Risk';\n            case 'red':\n                return 'High Risk';\n            default:\n                return band;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                        fontSize: \"lg\",\n                        fontWeight: \"600\",\n                        color: \"neutral.700\",\n                        children: \"Loading Analytics...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"lg\",\n                            fontWeight: \"600\",\n                            color: \"red.600\",\n                            mb: 2,\n                            children: \"Failed to load analytics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"sm\",\n                            color: \"red.500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (!analytics) {\n        return null;\n    }\n    const totalRiskCount = analytics.risk_distribution.green + analytics.risk_distribution.yellow + analytics.risk_distribution.red;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    mb: 2,\n                                    children: \"Analytics Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    color: \"neutral.600\",\n                                    fontSize: \"sm\",\n                                    children: [\n                                        \"Last updated: \",\n                                        new Date(analytics.last_updated).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/portfolio'),\n                            children: \"View Portfolio\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    gap: 6,\n                    mb: 8,\n                    direction: {\n                        base: 'column',\n                        lg: 'row'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Total MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"neutral.800\",\n                                    mb: 2,\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_msmes)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    colorScheme: \"brand\",\n                                    onClick: ()=>router.push('/portfolio'),\n                                    children: \"View All →\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Total Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"neutral.800\",\n                                    mb: 2,\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_signals)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        analytics.average_signals_per_msme.toFixed(1),\n                                        \" avg per MSME\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Low Risk MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"green.600\",\n                                    mb: 2,\n                                    children: analytics.risk_distribution.green\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        totalRiskCount > 0 ? (analytics.risk_distribution.green / totalRiskCount * 100).toFixed(1) : 0,\n                                        \"% of portfolio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"High Risk MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"red.600\",\n                                    mb: 2,\n                                    children: analytics.risk_distribution.red\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        totalRiskCount > 0 ? (analytics.risk_distribution.red / totalRiskCount * 100).toFixed(1) : 0,\n                                        \"% of portfolio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        lg: 2\n                    },\n                    spacing: 6,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Risk Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.risk_distribution).map((param)=>{\n                                            let [risk, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                                        variant: \"subtle\",\n                                                                        borderRadius: \"full\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        fontSize: \"xs\",\n                                                                        fontWeight: \"600\",\n                                                                        children: getRiskBandLabel(risk)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    totalRiskCount > 0 ? (count / totalRiskCount * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                        value: totalRiskCount > 0 ? count / totalRiskCount * 100 : 0,\n                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, risk, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Business Type Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.business_type_distribution).map((param)=>{\n                                            let [type, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        name: type === 'retail' ? 'Store' : type === 'b2b' ? 'Building' : type === 'services' ? 'Tool' : 'Factory',\n                                                                        size: 16,\n                                                                        color: \"neutral.600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.700\",\n                                                                        textTransform: \"capitalize\",\n                                                                        fontWeight: \"500\",\n                                                                        children: type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    analytics.total_msmes > 0 ? (count / analytics.total_msmes * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                        value: analytics.total_msmes > 0 ? count / analytics.total_msmes * 100 : 0,\n                                                        colorScheme: \"brand\",\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, type, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"t507Zj7k0FGCuaLMMY/kfCFOcuI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});