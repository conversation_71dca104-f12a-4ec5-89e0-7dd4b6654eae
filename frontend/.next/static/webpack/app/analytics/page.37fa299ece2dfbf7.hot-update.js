"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fetchAnalytics = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching analytics data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getDashboardAnalytics();\n            console.log('Analytics data received:', data);\n            setAnalytics(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching analytics:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch analytics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPage.useEffect\": ()=>{\n            fetchAnalytics();\n        }\n    }[\"AnalyticsPage.useEffect\"], []);\n    const getRiskBandColor = (band)=>{\n        switch(band){\n            case 'green':\n                return 'success.500';\n            case 'yellow':\n                return 'warning.500';\n            case 'red':\n                return 'danger.500';\n            default:\n                return 'neutral.500';\n        }\n    };\n    const getRiskBandLabel = (band)=>{\n        switch(band){\n            case 'green':\n                return 'Low Risk';\n            case 'yellow':\n                return 'Medium Risk';\n            case 'red':\n                return 'High Risk';\n            default:\n                return band;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                        spacing: 6,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                w: 16,\n                                h: 16,\n                                bg: \"brand.100\",\n                                borderRadius: \"full\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Spinner, {\n                                    size: \"lg\",\n                                    color: \"brand.600\",\n                                    thickness: \"3px\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"600\",\n                                        color: \"neutral.700\",\n                                        children: \"Loading Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"neutral.500\",\n                                        children: \"Fetching dashboard data...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"danger.200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert.Root, {\n                        status: \"error\",\n                        bg: \"danger.50\",\n                        borderRadius: \"lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert.Indicator, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                align: \"start\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontWeight: \"600\",\n                                        color: \"danger.800\",\n                                        children: \"Failed to load analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"danger.600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    }\n    if (!analytics) {\n        return null;\n    }\n    const totalRiskCount = analytics.risk_distribution.green + analytics.risk_distribution.yellow + analytics.risk_distribution.red;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                            align: \"start\",\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    children: \"Analytics Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    color: \"neutral.600\",\n                                    fontSize: \"sm\",\n                                    children: [\n                                        \"Last updated: \",\n                                        new Date(analytics.last_updated).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                name: \"Users\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 23\n                            }, void 0),\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/portfolio'),\n                            shadow: \"sm\",\n                            _hover: {\n                                shadow: 'md',\n                                transform: 'translateY(-1px)'\n                            },\n                            children: \"View Portfolio\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        md: 2,\n                        lg: 4\n                    },\n                    spacing: 6,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"brand.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"brand.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"Users\",\n                                                        size: 20,\n                                                        color: \"brand.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Total MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"neutral.800\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_msmes)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            size: \"sm\",\n                                            variant: \"ghost\",\n                                            colorScheme: \"brand\",\n                                            onClick: ()=>router.push('/portfolio'),\n                                            rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                name: \"ChevronRight\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"info.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"info.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"Activity\",\n                                                        size: 20,\n                                                        color: \"info.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Total Signals\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"neutral.800\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_signals)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                analytics.average_signals_per_msme.toFixed(1),\n                                                \" avg per MSME\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"success.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"success.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"Shield\",\n                                                        size: 20,\n                                                        color: \"success.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Low Risk MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"success.600\",\n                                                            children: analytics.risk_distribution.green\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                totalRiskCount > 0 ? (analytics.risk_distribution.green / totalRiskCount * 100).toFixed(1) : 0,\n                                                \"% of portfolio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"danger.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"danger.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"AlertCircle\",\n                                                        size: 20,\n                                                        color: \"danger.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"High Risk MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"danger.600\",\n                                                            children: analytics.risk_distribution.red\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                totalRiskCount > 0 ? (analytics.risk_distribution.red / totalRiskCount * 100).toFixed(1) : 0,\n                                                \"% of portfolio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        lg: 2\n                    },\n                    spacing: 6,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Risk Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.risk_distribution).map((param)=>{\n                                            let [risk, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                                        variant: \"subtle\",\n                                                                        borderRadius: \"full\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        fontSize: \"xs\",\n                                                                        fontWeight: \"600\",\n                                                                        children: getRiskBandLabel(risk)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    totalRiskCount > 0 ? (count / totalRiskCount * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                        value: totalRiskCount > 0 ? count / totalRiskCount * 100 : 0,\n                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, risk, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Business Type Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.business_type_distribution).map((param)=>{\n                                            let [type, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        name: type === 'retail' ? 'Store' : type === 'b2b' ? 'Building' : type === 'services' ? 'Tool' : 'Factory',\n                                                                        size: 16,\n                                                                        color: \"neutral.600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.700\",\n                                                                        textTransform: \"capitalize\",\n                                                                        fontWeight: \"500\",\n                                                                        children: type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    analytics.total_msmes > 0 ? (count / analytics.total_msmes * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                        value: analytics.total_msmes > 0 ? count / analytics.total_msmes * 100 : 0,\n                                                        colorScheme: \"brand\",\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, type, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"t507Zj7k0FGCuaLMMY/kfCFOcuI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});