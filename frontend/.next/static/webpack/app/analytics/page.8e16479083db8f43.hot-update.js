"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fetchAnalytics = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching analytics data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getDashboardAnalytics();\n            console.log('Analytics data received:', data);\n            setAnalytics(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching analytics:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch analytics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPage.useEffect\": ()=>{\n            fetchAnalytics();\n        }\n    }[\"AnalyticsPage.useEffect\"], []);\n    const getRiskBandColor = (band)=>{\n        switch(band){\n            case 'green':\n                return 'success.500';\n            case 'yellow':\n                return 'warning.500';\n            case 'red':\n                return 'danger.500';\n            default:\n                return 'neutral.500';\n        }\n    };\n    const getRiskBandLabel = (band)=>{\n        switch(band){\n            case 'green':\n                return 'Low Risk';\n            case 'yellow':\n                return 'Medium Risk';\n            case 'red':\n                return 'High Risk';\n            default:\n                return band;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                        fontSize: \"lg\",\n                        fontWeight: \"600\",\n                        color: \"neutral.700\",\n                        children: \"Loading Analytics...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"lg\",\n                            fontWeight: \"600\",\n                            color: \"red.600\",\n                            mb: 2,\n                            children: \"Failed to load analytics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"sm\",\n                            color: \"red.500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (!analytics) {\n        return null;\n    }\n    const totalRiskCount = analytics.risk_distribution.green + analytics.risk_distribution.yellow + analytics.risk_distribution.red;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    mb: 2,\n                                    children: \"Analytics Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    color: \"neutral.600\",\n                                    fontSize: \"sm\",\n                                    children: [\n                                        \"Last updated: \",\n                                        new Date(analytics.last_updated).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/portfolio'),\n                            children: \"View Portfolio\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    gap: 6,\n                    mb: 8,\n                    direction: {\n                        base: 'column',\n                        lg: 'row'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Total MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"neutral.800\",\n                                    mb: 2,\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_msmes)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    colorScheme: \"brand\",\n                                    onClick: ()=>router.push('/portfolio'),\n                                    children: \"View All →\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Total Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"neutral.800\",\n                                    mb: 2,\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_signals)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        analytics.average_signals_per_msme.toFixed(1),\n                                        \" avg per MSME\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"Low Risk MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"green.600\",\n                                    mb: 2,\n                                    children: analytics.risk_distribution.green\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        totalRiskCount > 0 ? (analytics.risk_distribution.green / totalRiskCount * 100).toFixed(1) : 0,\n                                        \"% of portfolio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.500\",\n                                    mb: 1,\n                                    children: \"High Risk MSMEs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"3xl\",\n                                    fontWeight: \"700\",\n                                    color: \"red.600\",\n                                    mb: 2,\n                                    children: analytics.risk_distribution.red\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    fontSize: \"sm\",\n                                    color: \"neutral.600\",\n                                    children: [\n                                        totalRiskCount > 0 ? (analytics.risk_distribution.red / totalRiskCount * 100).toFixed(1) : 0,\n                                        \"% of portfolio\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    gap: 6,\n                    direction: {\n                        base: 'column',\n                        lg: 'row'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"md\",\n                                    color: \"neutral.800\",\n                                    mb: 4,\n                                    children: \"Risk Distribution\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                    children: Object.entries(analytics.risk_distribution).map((param)=>{\n                                        let [risk, count] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                            justify: \"space-between\",\n                                            align: \"center\",\n                                            mb: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                                    align: \"center\",\n                                                    gap: 3,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                            colorScheme: risk === 'green' ? 'green' : risk === 'yellow' ? 'yellow' : 'red',\n                                                            variant: \"subtle\",\n                                                            px: 2,\n                                                            py: 1,\n                                                            fontSize: \"xs\",\n                                                            children: getRiskBandLabel(risk)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"sm\",\n                                                            color: \"neutral.600\",\n                                                            children: [\n                                                                count,\n                                                                \" MSMEs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"sm\",\n                                                    fontWeight: \"600\",\n                                                    color: \"neutral.700\",\n                                                    children: [\n                                                        totalRiskCount > 0 ? (count / totalRiskCount * 100).toFixed(1) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, risk, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"white\",\n                            p: 6,\n                            borderRadius: \"xl\",\n                            shadow: \"sm\",\n                            flex: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"md\",\n                                    color: \"neutral.800\",\n                                    mb: 4,\n                                    children: \"Business Type Distribution\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                    children: Object.entries(analytics.business_type_distribution).map((param)=>{\n                                        let [type, count] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                            justify: \"space-between\",\n                                            align: \"center\",\n                                            mb: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                                                    align: \"center\",\n                                                    gap: 3,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"sm\",\n                                                            color: \"neutral.700\",\n                                                            textTransform: \"capitalize\",\n                                                            fontWeight: \"500\",\n                                                            children: type\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"sm\",\n                                                            color: \"neutral.600\",\n                                                            children: [\n                                                                count,\n                                                                \" MSMEs\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                    fontSize: \"sm\",\n                                                    fontWeight: \"600\",\n                                                    color: \"neutral.700\",\n                                                    children: [\n                                                        analytics.total_msmes > 0 ? (count / analytics.total_msmes * 100).toFixed(1) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"t507Zj7k0FGCuaLMMY/kfCFOcuI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});