"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Container,Flex,Heading,Text!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fetchAnalytics = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching analytics data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getDashboardAnalytics();\n            console.log('Analytics data received:', data);\n            setAnalytics(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching analytics:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch analytics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPage.useEffect\": ()=>{\n            fetchAnalytics();\n        }\n    }[\"AnalyticsPage.useEffect\"], []);\n    const getRiskBandColor = (band)=>{\n        switch(band){\n            case 'green':\n                return 'success.500';\n            case 'yellow':\n                return 'warning.500';\n            case 'red':\n                return 'danger.500';\n            default:\n                return 'neutral.500';\n        }\n    };\n    const getRiskBandLabel = (band)=>{\n        switch(band){\n            case 'green':\n                return 'Low Risk';\n            case 'yellow':\n                return 'Medium Risk';\n            case 'red':\n                return 'High Risk';\n            default:\n                return band;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                        fontSize: \"lg\",\n                        fontWeight: \"600\",\n                        color: \"neutral.700\",\n                        children: \"Loading Analytics...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"lg\",\n                            fontWeight: \"600\",\n                            color: \"red.600\",\n                            mb: 2,\n                            children: \"Failed to load analytics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            fontSize: \"sm\",\n                            color: \"red.500\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (!analytics) {\n        return null;\n    }\n    const totalRiskCount = analytics.risk_distribution.green + analytics.risk_distribution.yellow + analytics.risk_distribution.red;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    mb: 2,\n                                    children: \"Analytics Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                    color: \"neutral.600\",\n                                    fontSize: \"sm\",\n                                    children: [\n                                        \"Last updated: \",\n                                        new Date(analytics.last_updated).toLocaleDateString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/portfolio'),\n                            children: \"View Portfolio\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        md: 2,\n                        lg: 4\n                    },\n                    spacing: 6,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"brand.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"brand.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"Users\",\n                                                        size: 20,\n                                                        color: \"brand.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Total MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"neutral.800\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_msmes)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                            size: \"sm\",\n                                            variant: \"ghost\",\n                                            colorScheme: \"brand\",\n                                            onClick: ()=>router.push('/portfolio'),\n                                            rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                name: \"ChevronRight\",\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"info.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"info.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"Activity\",\n                                                        size: 20,\n                                                        color: \"info.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Total Signals\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"neutral.800\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(analytics.total_signals)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                analytics.average_signals_per_msme.toFixed(1),\n                                                \" avg per MSME\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"success.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"success.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"Shield\",\n                                                        size: 20,\n                                                        color: \"success.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"Low Risk MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"success.600\",\n                                                            children: analytics.risk_distribution.green\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                totalRiskCount > 0 ? (analytics.risk_distribution.green / totalRiskCount * 100).toFixed(1) : 0,\n                                                \"% of portfolio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                    align: \"start\",\n                                    spacing: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                            spacing: 3,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                    p: 2,\n                                                    bg: \"danger.100\",\n                                                    borderRadius: \"lg\",\n                                                    border: \"1px solid\",\n                                                    borderColor: \"danger.200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        name: \"AlertCircle\",\n                                                        size: 20,\n                                                        color: \"danger.600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                                    align: \"start\",\n                                                    spacing: 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"xs\",\n                                                            color: \"neutral.500\",\n                                                            fontWeight: \"500\",\n                                                            children: \"High Risk MSMEs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            fontSize: \"2xl\",\n                                                            fontWeight: \"700\",\n                                                            color: \"danger.600\",\n                                                            children: analytics.risk_distribution.red\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"neutral.600\",\n                                            children: [\n                                                totalRiskCount > 0 ? (analytics.risk_distribution.red / totalRiskCount * 100).toFixed(1) : 0,\n                                                \"% of portfolio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        lg: 2\n                    },\n                    spacing: 6,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Risk Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.risk_distribution).map((param)=>{\n                                            let [risk, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                                        variant: \"subtle\",\n                                                                        borderRadius: \"full\",\n                                                                        px: 2,\n                                                                        py: 1,\n                                                                        fontSize: \"xs\",\n                                                                        fontWeight: \"600\",\n                                                                        children: getRiskBandLabel(risk)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    totalRiskCount > 0 ? (count / totalRiskCount * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                        value: totalRiskCount > 0 ? count / totalRiskCount * 100 : 0,\n                                                        colorScheme: risk === 'green' ? 'success' : risk === 'yellow' ? 'warning' : 'danger',\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, risk, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Heading, {\n                                        size: \"md\",\n                                        color: \"neutral.800\",\n                                        children: \"Business Type Distribution\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardBody, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: Object.entries(analytics.business_type_distribution).map((param)=>{\n                                            let [type, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                        justify: \"space-between\",\n                                                        mb: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HStack, {\n                                                                spacing: 2,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        name: type === 'retail' ? 'Store' : type === 'b2b' ? 'Building' : type === 'services' ? 'Tool' : 'Factory',\n                                                                        size: 16,\n                                                                        color: \"neutral.600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.700\",\n                                                                        textTransform: \"capitalize\",\n                                                                        fontWeight: \"500\",\n                                                                        children: type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                        fontSize: \"sm\",\n                                                                        color: \"neutral.600\",\n                                                                        children: [\n                                                                            count,\n                                                                            \" MSMEs\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Container_Flex_Heading_Text_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                fontSize: \"sm\",\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.700\",\n                                                                children: [\n                                                                    analytics.total_msmes > 0 ? (count / analytics.total_msmes * 100).toFixed(1) : 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                        value: analytics.total_msmes > 0 ? count / analytics.total_msmes * 100 : 0,\n                                                        colorScheme: \"brand\",\n                                                        size: \"sm\",\n                                                        borderRadius: \"full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, type, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/analytics/page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"t507Zj7k0FGCuaLMMY/kfCFOcuI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});