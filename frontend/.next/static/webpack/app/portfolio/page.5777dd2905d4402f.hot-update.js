"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/portfolio/page",{

/***/ "(app-pages-browser)/./src/app/portfolio/page.tsx":
/*!************************************!*\
  !*** ./src/app/portfolio/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PortfolioPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/stack/v-stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/spinner/spinner.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/alert/namespace.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/stack/h-stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/input/input.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/select/namespace.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/card/namespace.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Box,Button,Card,CardBody,CardHeader,Container,Flex,HStack,Heading,Input,Select,SimpleGrid,Spinner,Text,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/card/card.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Icon */ \"(app-pages-browser)/./src/components/ui/Icon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PortfolioPage() {\n    _s();\n    const [msmes, setMsmes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMsmes, setFilteredMsmes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [riskFilter, setRiskFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [businessTypeFilter, setBusinessTypeFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fetchPortfolio = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching portfolio data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getPortfolio();\n            console.log('Portfolio data received:', data);\n            setMsmes(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching portfolio:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch portfolio');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMsmes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PortfolioPage.useCallback[filterMsmes]\": ()=>{\n            let filtered = msmes;\n            // Search filter\n            if (searchTerm) {\n                filtered = filtered.filter({\n                    \"PortfolioPage.useCallback[filterMsmes]\": (msme)=>msme.name.toLowerCase().includes(searchTerm.toLowerCase()) || msme.location.toLowerCase().includes(searchTerm.toLowerCase())\n                }[\"PortfolioPage.useCallback[filterMsmes]\"]);\n            }\n            // Risk band filter\n            if (riskFilter !== 'all') {\n                filtered = filtered.filter({\n                    \"PortfolioPage.useCallback[filterMsmes]\": (msme)=>msme.risk_band === riskFilter\n                }[\"PortfolioPage.useCallback[filterMsmes]\"]);\n            }\n            // Business type filter\n            if (businessTypeFilter !== 'all') {\n                filtered = filtered.filter({\n                    \"PortfolioPage.useCallback[filterMsmes]\": (msme)=>msme.business_type === businessTypeFilter\n                }[\"PortfolioPage.useCallback[filterMsmes]\"]);\n            }\n            setFilteredMsmes(filtered);\n        }\n    }[\"PortfolioPage.useCallback[filterMsmes]\"], [\n        msmes,\n        searchTerm,\n        riskFilter,\n        businessTypeFilter\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PortfolioPage.useEffect\": ()=>{\n            fetchPortfolio();\n        }\n    }[\"PortfolioPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PortfolioPage.useEffect\": ()=>{\n            filterMsmes();\n        }\n    }[\"PortfolioPage.useEffect\"], [\n        filterMsmes\n    ]);\n    const handleMSMEClick = (msmeId)=>{\n        router.push(\"/msme/\".concat(msmeId));\n    };\n    const getRiskBandLabel = (riskBand)=>{\n        switch(riskBand){\n            case 'green':\n                return 'Low';\n            case 'yellow':\n                return 'Medium';\n            case 'red':\n                return 'High';\n            default:\n                return riskBand;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                        spacing: 6,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                w: 16,\n                                h: 16,\n                                bg: \"brand.100\",\n                                borderRadius: \"full\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Spinner, {\n                                    size: \"lg\",\n                                    color: \"brand.600\",\n                                    thickness: \"3px\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"600\",\n                                        color: \"neutral.700\",\n                                        children: \"Loading Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"neutral.500\",\n                                        children: \"Fetching your MSME data...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"danger.200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__, {\n                        status: \"error\",\n                        bg: \"danger.50\",\n                        borderRadius: \"lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertIcon, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                align: \"start\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontWeight: \"600\",\n                                        color: \"danger.800\",\n                                        children: \"Failed to load portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"danger.600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                            align: \"start\",\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    children: \"Portfolio Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.HStack, {\n                                    spacing: 4,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Badge, {\n                                            colorScheme: \"brand\",\n                                            variant: \"subtle\",\n                                            px: 3,\n                                            py: 1,\n                                            borderRadius: \"full\",\n                                            fontSize: \"sm\",\n                                            fontWeight: \"600\",\n                                            children: [\n                                                filteredMsmes.length,\n                                                \" of \",\n                                                msmes.length,\n                                                \" MSMEs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                            color: \"neutral.600\",\n                                            fontSize: \"sm\",\n                                            children: [\n                                                \"Last updated: \",\n                                                new Date().toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                name: \"Plus\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 23\n                            }, void 0),\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/msme/new'),\n                            shadow: \"sm\",\n                            _hover: {\n                                shadow: 'md',\n                                transform: 'translateY(-1px)'\n                            },\n                            children: \"Add MSME\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    bg: \"white\",\n                    p: 6,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"neutral.200\",\n                    mb: 6,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.SimpleGrid, {\n                        columns: {\n                            base: 1,\n                            md: 3\n                        },\n                        spacing: 4,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                position: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Input, {\n                                        placeholder: \"Search MSMEs...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        bg: \"neutral.50\",\n                                        border: \"1px solid\",\n                                        borderColor: \"neutral.200\",\n                                        pl: 10,\n                                        _focus: {\n                                            borderColor: 'brand.400',\n                                            bg: 'white',\n                                            shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)'\n                                        },\n                                        _hover: {\n                                            borderColor: 'neutral.300'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                        position: \"absolute\",\n                                        left: 3,\n                                        top: \"50%\",\n                                        transform: \"translateY(-50%)\",\n                                        pointerEvents: \"none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                            name: \"Search\",\n                                            size: 16,\n                                            color: \"neutral.400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__, {\n                                value: riskFilter,\n                                onChange: (e)=>setRiskFilter(e.target.value),\n                                bg: \"neutral.50\",\n                                border: \"1px solid\",\n                                borderColor: \"neutral.200\",\n                                _focus: {\n                                    borderColor: 'brand.400',\n                                    bg: 'white',\n                                    shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)'\n                                },\n                                _hover: {\n                                    borderColor: 'neutral.300'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"All Risk Bands\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"red\",\n                                        children: \"High Risk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"yellow\",\n                                        children: \"Medium Risk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"green\",\n                                        children: \"Low Risk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__, {\n                                value: businessTypeFilter,\n                                onChange: (e)=>setBusinessTypeFilter(e.target.value),\n                                bg: \"neutral.50\",\n                                border: \"1px solid\",\n                                borderColor: \"neutral.200\",\n                                _focus: {\n                                    borderColor: 'brand.400',\n                                    bg: 'white',\n                                    shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)'\n                                },\n                                _hover: {\n                                    borderColor: 'neutral.300'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"All Business Types\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"retail\",\n                                        children: \"Retail\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"b2b\",\n                                        children: \"B2B\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"services\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"manufacturing\",\n                                        children: \"Manufacturing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.SimpleGrid, {\n                    columns: {\n                        base: 1,\n                        md: 2,\n                        lg: 3\n                    },\n                    spacing: 6,\n                    children: filteredMsmes.map((msme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__, {\n                            cursor: \"pointer\",\n                            onClick: ()=>handleMSMEClick(msme.msme_id),\n                            _hover: {\n                                transform: 'translateY(-2px)',\n                                shadow: 'lg'\n                            },\n                            transition: \"all 0.2s ease-in-out\",\n                            bg: \"white\",\n                            border: \"1px solid\",\n                            borderColor: \"neutral.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.CardHeader, {\n                                    pb: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.HStack, {\n                                        justify: \"space-between\",\n                                        align: \"start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.HStack, {\n                                                spacing: 3,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                        p: 2,\n                                                        bg: \"\".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColorScheme)(msme.risk_band), \".50\"),\n                                                        borderRadius: \"lg\",\n                                                        border: \"1px solid\",\n                                                        borderColor: \"\".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColorScheme)(msme.risk_band), \".200\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                            name: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getBusinessTypeIcon)(msme.business_type),\n                                                            size: 20,\n                                                            color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColor)(msme.risk_band)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"start\",\n                                                        spacing: 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontWeight: \"600\",\n                                                                color: \"neutral.800\",\n                                                                fontSize: \"lg\",\n                                                                children: msme.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"sm\",\n                                                                color: \"neutral.500\",\n                                                                textTransform: \"capitalize\",\n                                                                children: msme.business_type\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Badge, {\n                                                colorScheme: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColorScheme)(msme.risk_band),\n                                                variant: \"subtle\",\n                                                borderRadius: \"full\",\n                                                px: 2,\n                                                py: 1,\n                                                fontSize: \"xs\",\n                                                fontWeight: \"600\",\n                                                children: getRiskBandLabel(msme.risk_band)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.CardBody, {\n                                    pt: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                        spacing: 4,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.HStack, {\n                                                justify: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"start\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"xs\",\n                                                                color: \"neutral.500\",\n                                                                fontWeight: \"500\",\n                                                                children: \"Credit Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"2xl\",\n                                                                fontWeight: \"700\",\n                                                                color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColor)(msme.risk_band),\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatScore)(msme.current_score)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"end\",\n                                                        spacing: 1,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.HStack, {\n                                                            spacing: 1,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                    name: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getTrendIcon)(msme.score_trend),\n                                                                    size: 14,\n                                                                    color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getTrendColor)(msme.score_trend)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fontSize: \"sm\",\n                                                                    color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getTrendColor)(msme.score_trend),\n                                                                    textTransform: \"capitalize\",\n                                                                    fontWeight: \"600\",\n                                                                    children: msme.score_trend\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.HStack, {\n                                                justify: \"space-between\",\n                                                fontSize: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"start\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                color: \"neutral.500\",\n                                                                fontWeight: \"500\",\n                                                                children: \"Location\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                color: \"neutral.700\",\n                                                                fontWeight: \"600\",\n                                                                children: msme.location\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"end\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                color: \"neutral.500\",\n                                                                fontWeight: \"500\",\n                                                                children: \"Signals\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Badge, {\n                                                                colorScheme: \"brand\",\n                                                                variant: \"subtle\",\n                                                                borderRadius: \"full\",\n                                                                px: 2,\n                                                                py: 1,\n                                                                fontSize: \"xs\",\n                                                                fontWeight: \"600\",\n                                                                children: msme.signals_count\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this),\n                                            msme.last_signal_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                fontSize: \"xs\",\n                                                color: \"neutral.400\",\n                                                children: [\n                                                    \"Last activity: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDate)(msme.last_signal_date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, msme.msme_id, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                filteredMsmes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    textAlign: \"center\",\n                    py: 12,\n                    bg: \"white\",\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"neutral.200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                        spacing: 4,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                name: \"Search\",\n                                size: 48,\n                                color: \"neutral.400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"600\",\n                                        color: \"neutral.600\",\n                                        children: \"No MSMEs found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Box_Button_Card_CardBody_CardHeader_Container_Flex_HStack_Heading_Input_Select_SimpleGrid_Spinner_Text_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"neutral.500\",\n                                        children: \"Try adjusting your search criteria or filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(PortfolioPage, \"WUrbgX4zwa5Zmk+TLfsm/Gw/Fj0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PortfolioPage;\nvar _c;\n$RefreshReg$(_c, \"PortfolioPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/portfolio/page.tsx\n"));

/***/ })

});