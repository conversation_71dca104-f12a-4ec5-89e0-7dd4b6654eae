"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/portfolio/page",{

/***/ "(app-pages-browser)/./src/app/portfolio/page.tsx":
/*!************************************!*\
  !*** ./src/app/portfolio/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PortfolioPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/box/box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/container/container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/flex/flex.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/stack/v-stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/spinner/spinner.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/text.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/alert/namespace.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/__barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!./node_modules/@chakra-ui/react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/typography/heading.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/stack/h-stack.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/badge/badge.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/button/button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/icon/icon.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/grid/simple-grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/input/input-group.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/input/input.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/select/namespace.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!@chakra-ui/react */ \"(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/table/namespace.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Icon */ \"(app-pages-browser)/./src/components/ui/Icon.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction PortfolioPage() {\n    _s();\n    const [msmes, setMsmes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMsmes, setFilteredMsmes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [riskFilter, setRiskFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [businessTypeFilter, setBusinessTypeFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const cardBg = 'white';\n    const borderColor = 'gray.200';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PortfolioPage.useEffect\": ()=>{\n            fetchPortfolio();\n        }\n    }[\"PortfolioPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PortfolioPage.useEffect\": ()=>{\n            filterMsmes();\n        }\n    }[\"PortfolioPage.useEffect\"], [\n        msmes,\n        searchTerm,\n        riskFilter,\n        businessTypeFilter\n    ]);\n    const fetchPortfolio = async ()=>{\n        try {\n            setLoading(true);\n            console.log('Fetching portfolio data...');\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getPortfolio();\n            console.log('Portfolio data received:', data);\n            setMsmes(data);\n            setError(null);\n        } catch (err) {\n            console.error('Error fetching portfolio:', err);\n            setError(err instanceof Error ? err.message : 'Failed to fetch portfolio');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMsmes = ()=>{\n        let filtered = msmes;\n        // Search filter\n        if (searchTerm) {\n            filtered = filtered.filter((msme)=>msme.name.toLowerCase().includes(searchTerm.toLowerCase()) || msme.location.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // Risk band filter\n        if (riskFilter !== 'all') {\n            filtered = filtered.filter((msme)=>msme.risk_band === riskFilter);\n        }\n        // Business type filter\n        if (businessTypeFilter !== 'all') {\n            filtered = filtered.filter((msme)=>msme.business_type === businessTypeFilter);\n        }\n        setFilteredMsmes(filtered);\n    };\n    const handleMSMEClick = (msmeId)=>{\n        router.push(\"/msme/\".concat(msmeId));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                    justify: \"center\",\n                    align: \"center\",\n                    minH: \"400px\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                        spacing: 6,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                w: 16,\n                                h: 16,\n                                bg: \"brand.100\",\n                                borderRadius: \"full\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Spinner, {\n                                    size: \"lg\",\n                                    color: \"brand.600\",\n                                    thickness: \"3px\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"600\",\n                                        color: \"neutral.700\",\n                                        children: \"Loading Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"neutral.500\",\n                                        children: \"Fetching your MSME data...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n            bg: \"neutral.50\",\n            minH: \"100vh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    bg: \"white\",\n                    p: 8,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"danger.200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__, {\n                        status: \"error\",\n                        bg: \"danger.50\",\n                        borderRadius: \"lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.AlertIcon, {\n                                color: \"danger.600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                align: \"start\",\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontWeight: \"600\",\n                                        color: \"danger.800\",\n                                        children: \"Failed to load portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"danger.600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n        bg: \"neutral.50\",\n        minH: \"100vh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                    justify: \"space-between\",\n                    align: \"center\",\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                            align: \"start\",\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Heading, {\n                                    size: \"xl\",\n                                    color: \"neutral.800\",\n                                    fontWeight: \"700\",\n                                    children: \"Portfolio Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.HStack, {\n                                    spacing: 4,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Badge, {\n                                            colorScheme: \"brand\",\n                                            variant: \"subtle\",\n                                            px: 3,\n                                            py: 1,\n                                            borderRadius: \"full\",\n                                            fontSize: \"sm\",\n                                            fontWeight: \"600\",\n                                            children: [\n                                                filteredMsmes.length,\n                                                \" of \",\n                                                msmes.length,\n                                                \" MSMEs\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                            color: \"neutral.600\",\n                                            fontSize: \"sm\",\n                                            children: [\n                                                \"Last updated: \",\n                                                new Date().toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Icon, {\n                                as: _barrel_optimize_names_BarChart3_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 23\n                            }, void 0),\n                            colorScheme: \"brand\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/msme/new'),\n                            shadow: \"sm\",\n                            _hover: {\n                                shadow: 'md',\n                                transform: 'translateY(-1px)'\n                            },\n                            children: \"Add MSME\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    bg: \"white\",\n                    p: 6,\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"neutral.200\",\n                    mb: 6,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.SimpleGrid, {\n                        columns: {\n                            base: 1,\n                            md: 3\n                        },\n                        spacing: 4,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.InputGroup, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.InputLeftElement, {\n                                        pointerEvents: \"none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Icon, {\n                                            as: _barrel_optimize_names_BarChart3_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                            color: \"neutral.400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Input, {\n                                        placeholder: \"Search MSMEs...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        bg: \"neutral.50\",\n                                        border: \"1px solid\",\n                                        borderColor: \"neutral.200\",\n                                        _focus: {\n                                            borderColor: 'brand.400',\n                                            bg: 'white',\n                                            shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)'\n                                        },\n                                        _hover: {\n                                            borderColor: 'neutral.300'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__, {\n                                value: riskFilter,\n                                onChange: (e)=>setRiskFilter(e.target.value),\n                                bg: \"neutral.50\",\n                                border: \"1px solid\",\n                                borderColor: \"neutral.200\",\n                                _focus: {\n                                    borderColor: 'brand.400',\n                                    bg: 'white',\n                                    shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)'\n                                },\n                                _hover: {\n                                    borderColor: 'neutral.300'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"All Risk Bands\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"red\",\n                                        children: \"High Risk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"yellow\",\n                                        children: \"Medium Risk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"green\",\n                                        children: \"Low Risk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__, {\n                                value: businessTypeFilter,\n                                onChange: (e)=>setBusinessTypeFilter(e.target.value),\n                                bg: \"neutral.50\",\n                                border: \"1px solid\",\n                                borderColor: \"neutral.200\",\n                                _focus: {\n                                    borderColor: 'brand.400',\n                                    bg: 'white',\n                                    shadow: '0 0 0 3px rgba(14, 165, 233, 0.1)'\n                                },\n                                _hover: {\n                                    borderColor: 'neutral.300'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"All Business Types\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"retail\",\n                                        children: \"Retail\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"b2b\",\n                                        children: \"B2B\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"services\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"manufacturing\",\n                                        children: \"Manufacturing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    bg: \"white\",\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"neutral.200\",\n                    overflow: \"hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.TableContainer, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__, {\n                            variant: \"simple\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Thead, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Tr, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Business\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Credit Score\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Risk Band\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Trend\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Signals\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Last Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Th, {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Tbody, {\n                                    children: filteredMsmes.map((msme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Tr, {\n                                            cursor: \"pointer\",\n                                            _hover: {\n                                                bg: 'neutral.50',\n                                                transform: 'translateY(-1px)',\n                                                shadow: 'sm'\n                                            },\n                                            onClick: ()=>handleMSMEClick(msme.msme_id),\n                                            transition: \"all 0.2s ease-in-out\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"start\",\n                                                        spacing: 2,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.HStack, {\n                                                            spacing: 3,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                                    p: 2,\n                                                                    bg: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandBg)(msme.risk_band),\n                                                                    borderRadius: \"lg\",\n                                                                    border: \"1px solid\",\n                                                                    borderColor: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColor)(msme.risk_band),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                        name: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getBusinessTypeIcon)(msme.business_type),\n                                                                        size: 16,\n                                                                        color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColor)(msme.risk_band)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                                    align: \"start\",\n                                                                    spacing: 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            fontWeight: \"600\",\n                                                                            color: \"neutral.800\",\n                                                                            children: msme.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            fontSize: \"xs\",\n                                                                            color: \"neutral.500\",\n                                                                            textTransform: \"capitalize\",\n                                                                            children: msme.business_type\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        color: \"neutral.600\",\n                                                        fontSize: \"sm\",\n                                                        children: msme.location\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"start\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"xl\",\n                                                                fontWeight: \"700\",\n                                                                color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColor)(msme.risk_band),\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatScore)(msme.current_score)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"xs\",\n                                                                color: \"neutral.500\",\n                                                                children: \"Credit Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Badge, {\n                                                        bg: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandBg)(msme.risk_band),\n                                                        color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColor)(msme.risk_band),\n                                                        border: \"1px solid\",\n                                                        borderColor: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getRiskBandColor)(msme.risk_band),\n                                                        px: 3,\n                                                        py: 1,\n                                                        borderRadius: \"full\",\n                                                        fontSize: \"xs\",\n                                                        fontWeight: \"600\",\n                                                        textTransform: \"capitalize\",\n                                                        children: msme.risk_band === 'green' ? 'Low' : msme.risk_band === 'yellow' ? 'Medium' : 'High'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.HStack, {\n                                                        spacing: 2,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Icon__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                name: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getTrendIcon)(msme.score_trend),\n                                                                size: 16,\n                                                                color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getTrendColor)(msme.score_trend)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"sm\",\n                                                                color: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getTrendColor)(msme.score_trend),\n                                                                textTransform: \"capitalize\",\n                                                                fontWeight: \"600\",\n                                                                children: msme.score_trend\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Badge, {\n                                                        colorScheme: \"brand\",\n                                                        variant: \"subtle\",\n                                                        borderRadius: \"full\",\n                                                        px: 2,\n                                                        py: 1,\n                                                        fontSize: \"xs\",\n                                                        fontWeight: \"600\",\n                                                        children: [\n                                                            msme.signals_count,\n                                                            \" signals\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                                        align: \"start\",\n                                                        spacing: 1,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"sm\",\n                                                                color: \"neutral.600\",\n                                                                children: msme.last_signal_date ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDate)(msme.last_signal_date) : 'No signals'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                fontSize: \"xs\",\n                                                                color: \"neutral.400\",\n                                                                children: \"Last activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Td, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        colorScheme: \"brand\",\n                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Icon, {\n                                                            as: _barrel_optimize_names_BarChart3_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 35\n                                                        }, void 0),\n                                                        borderRadius: \"lg\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            router.push(\"/msme/\".concat(msme.msme_id, \"/analytics\"));\n                                                        },\n                                                        _hover: {\n                                                            bg: 'brand.50',\n                                                            transform: 'translateY(-1px)',\n                                                            shadow: 'sm'\n                                                        },\n                                                        children: \"Analytics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, msme.msme_id, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                filteredMsmes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    textAlign: \"center\",\n                    py: 12,\n                    bg: \"white\",\n                    borderRadius: \"xl\",\n                    shadow: \"sm\",\n                    border: \"1px solid\",\n                    borderColor: \"neutral.200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                        spacing: 4,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Icon, {\n                                as: _barrel_optimize_names_BarChart3_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                boxSize: 12,\n                                color: \"neutral.400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.VStack, {\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"lg\",\n                                        fontWeight: \"600\",\n                                        color: \"neutral.600\",\n                                        children: \"No MSMEs found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Container_Flex_HStack_Heading_Icon_Input_InputGroup_InputLeftElement_Select_SimpleGrid_Spinner_Table_TableContainer_Tbody_Td_Text_Th_Thead_Tr_VStack_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"neutral.500\",\n                                        children: \"Try adjusting your search criteria or filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/frontend/src/app/portfolio/page.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n_s(PortfolioPage, \"6OiAleiKq2mXgDvjI6Bjzyo3hEs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PortfolioPage;\nvar _c;\n$RefreshReg$(_c, \"PortfolioPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/portfolio/page.tsx\n"));

/***/ })

});