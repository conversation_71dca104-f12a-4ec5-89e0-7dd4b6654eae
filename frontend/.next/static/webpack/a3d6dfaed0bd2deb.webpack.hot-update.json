{"c": ["app/layout", "app/portfolio/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/table/namespace.js", "(app-pages-browser)/./node_modules/@chakra-ui/react/dist/esm/components/table/table.js", "(app-pages-browser)/__barrel_optimize__?names=<PERSON><PERSON>,<PERSON>ertIcon,Badge,Box,Button,Container,Flex,HStack,Heading,Icon,Input,InputGroup,InputLeftElement,Select,SimpleGrid,Spinner,Table,TableContainer,Tbody,Td,Text,Th,Thead,Tr,VStack!=!./node_modules/@chakra-ui/react/dist/esm/index.js"]}